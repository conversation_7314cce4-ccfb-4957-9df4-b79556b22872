<!-- 数据集Tab组件 -->
<template>
  <div class="data-set-tab">
    <Block title="数据集管理" :enable-fixed-height="true" :enable-expand-content="true" :default-expand="false" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" type="primary" @click="onClickAdd">创建数据集数据源</el-button>
          <el-button size="small" type="primary" @click="onClickOperationButtonConfig">操作按钮隐藏配置</el-button>
          <el-button size="small" type="primary" @click="onClickDataSetTypeConfig">数据集类型配置</el-button>
          <el-button size="small" type="primary" @click="onClickDataSetCategory">数据集分类</el-button>
          <el-button size="small" type="primary" @click="onClickBatchImport">数据集导入</el-button>
          <el-button size="small" type="primary" @click="onClickBatchExport">数据集导出</el-button>
          <el-button size="small" type="primary" @click="onClickQualityMonitoringAssessment">质量监控与评估</el-button>
          <el-button size="small" type="primary" @click="onClickFieldTemplate">数据集业务表绑定</el-button>

          <el-dropdown @command="handleMoreCommand">
            <el-button size="small" type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="accessPermissionManagement">访问权限管理</el-dropdown-item>
                <el-dropdown-item command="qualityReportManagement">备份与恢复</el-dropdown-item>
                <el-dropdown-item command="dataSetIdentification">数据集合规性检查</el-dropdown-item>
                <el-dropdown-item command="integrationStrategy">数据校验</el-dropdown-item>
                <el-dropdown-item command="transformationRule">数据备份与恢复验证</el-dropdown-item>
                <el-dropdown-item command="formatFileConfig">数据安全性验证</el-dropdown-item>
                <el-dropdown-item command="dataSetAccessRule">安全性加固设置</el-dropdown-item>
                <el-dropdown-item command="accessAnalysisMonitor">日志管理</el-dropdown-item>
                <el-dropdown-item command="exceptionHandling">异常处理</el-dropdown-item>
                <el-dropdown-item command="sourceAnalysis">性能管理与监控</el-dropdown-item>
                <el-dropdown-item command="permissionAudit">权限审计</el-dropdown-item>
                <el-dropdown-item command="archiveManagement">数据归档</el-dropdown-item>
                <el-dropdown-item command="accessAudit">访问审计</el-dropdown-item>
                <el-dropdown-item command="logAnalysis">日志分析与行为监控</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>

      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <div class="search-row">
            <div class="search-fields">
              <el-form :model="searchForm" inline>
                <el-form-item label="数据集名称" label-width="100px">
                  <el-input v-model="searchForm.name" placeholder="请输入名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源名称" label-width="100px">
                  <el-input v-model="searchForm.dataSourceName" placeholder="请输入数据源名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="数据源类型" label-width="100px">
                  <el-select v-model="searchForm.dataSourceType" placeholder="请选择数据源类型" size="small" style="width: 160px" clearable>
                    <el-option label="MySql" value="MySql" />
                    <el-option label="Oracle" value="Oracle" />
                    <el-option label="SQL Server" value="SQL Server" />
                    <el-option label="达梦" value="达梦" />
                    <el-option label="Hive" value="Hive" />
                    <el-option label="MangoDB" value="MangoDB" />
                    <el-option label="Huawei GaussDB" value="Huawei GaussDB" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <div class="search-buttons">
              <el-button size="small" type="primary" @click="onSearch">查询</el-button>
              <el-button size="small" @click="onReset">重置</el-button>
              <el-button size="small" @click="$router.back()">返回</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <div style="width: 100%;">
        <BaseTableComp
          :data="paginatedData"
          :colData="tableColumns"
          :buttons="tableButtons"
          :current-page="pagination.page"
          :page-size="pagination.size"
          :total="pagination.total"
          :height="tableHeight"
          :visible-setting="false"
          @size-change="onSizeChange"
          @current-change="onPageChange"
          @selection-change="onSelectionChange"
          @clickButton="onTableButtonClick"
        />
      </div>
    </Block>

    <!-- 新增/编辑/详情弹窗 -->
    <Dialog
      width="35%"
      v-model="showDialogForm"
      :title="dialogMode === 'add' ? '新增数据集' : dialogMode === 'edit' ? '编辑数据集' : '数据集详情'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      :visible-confirm-button="dialogMode !== 'view'"
      :confirm-text="dialogMode === 'add' ? '新增' : '保存'"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <!-- 基础信息表单 -->
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :disabled="dialogMode === 'view'"
      />
    </Dialog>



    <!-- 数据集类型配置弹窗 -->
    <DataSetTypeConfigDialog
      v-model="showDataSetTypeConfigDialog"
    />

    <!-- 数据集分类弹窗 -->
    <DataSetCategoryDialog
      v-model="showDataSetCategoryDialog"
    />

    <!-- 质量监控与评估弹窗 -->
    <QualityMonitoringAssessmentDialog
      v-model="showQualityMonitoringAssessmentDialog"
    />

    <!-- 操作按钮隐藏配置弹窗 -->
    <OperationButtonConfigDialog
      v-model="showOperationButtonConfigDialog"
    />

    <!-- 访问权限调整弹窗 -->
    <Dialog
      v-model="showAccessPermissionDialog"
      title="访问权限调整"
      :destroy-on-close="true"
      width="600px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <el-form :model="accessPermissionForm" label-width="100px">
          <el-form-item label="数据集：">
            <el-select v-model="accessPermissionForm.dataSet" placeholder="请选择数据集" style="width: 100%">
              <el-option
                v-for="item in dataSetList"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="对象类型：">
            <el-select v-model="accessPermissionForm.objectType" style="width: 100%">
              <el-option label="用户" value="用户" />
              <el-option label="角色" value="角色" />
            </el-select>
          </el-form-item>

          <el-form-item label="用户/角色：">
            <el-select v-model="accessPermissionForm.objectSelection" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in getObjectSelectionData"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="权限范围：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.readPermission">库级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.tablePermission">表级权限</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.fieldPermission">字段权限</el-checkbox>
            </div>
          </el-form-item>

          <el-form-item label="权限设置：">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
              <el-checkbox v-model="accessPermissionForm.viewPermission">查看</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.editPermission">编辑</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.addPermission">新增</el-checkbox>
              <el-checkbox v-model="accessPermissionForm.deletePermission">删除</el-checkbox>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAccessPermissionDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAccessPermission">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 批量导入弹窗 -->
    <Dialog
      v-model="batchImportDialogVisible"
      title="数据集导入"
      width="600px"
      :destroy-on-close="true"
      :loading="importLoading"
      loading-text="导入中"
      confirm-text="上传"
      @click-confirm="uploadFile"
    >
      <div class="import-content">
        <div class="import-header">
          <el-icon><Upload /></el-icon>
          <span>导入须知</span>
        </div>

        <div class="import-steps">
          <div class="step-title">操作流程：</div>
          <div class="steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">下载模板</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">填写表格</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">上传表格</span>
            </div>
          </div>
        </div>

        <div class="template-download">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            导入模板下载
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            :file-list="importFileList"
            :on-remove="handleFileRemove"
            :on-change="handleFileSelect"
            :limit="1"
            accept=".xlsx,.xls"
            :auto-upload="false"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
            <template #tip>
              <div class="el-upload__tip">
                导入模式为业务表中已有数据更新，业务表中不含数据新增，仅支持后缀名为xlsx文件
              </div>
            </template>
          </el-upload>

          <!-- 显示选中的文件名 -->
          <div v-if="selectedFileName" class="selected-file">
            <div class="file-name">{{ selectedFileName }}</div>
          </div>
        </div>
      </div>
    </Dialog>
    <!-- 数据备份与恢复规则弹窗 -->
    <Dialog
      v-model="showBackupRuleDialog"
      title="数据集备份与恢复"
      :destroy-on-close="true"
      width="800px"
      confirm-text="保存"
      cancel-text="取消"
      @click-confirm="saveBackupRuleToCache"
      @click-cancel="showBackupRuleDialog = false"
    >
      <div class="dialog-content">
        <!-- 数据集备份规则 -->
        <div class="section-title">数据集备份规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="数据备份开始时间：">
              <el-date-picker
                v-model="backupRuleForm.backupStartTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="全量备份频率：">
              <el-select v-model="backupRuleForm.fullBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="增量备份频率：">
              <el-select v-model="backupRuleForm.incrementalBackupFrequency" style="width: 100%">
                <el-option label="每小时" value="每小时" />
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>

            <el-form-item label="备份数据清理策略：">
              <el-select v-model="backupRuleForm.dataCleanupPolicy" style="width: 100%">
                <el-option label="保留30天" value="保留30天" />
                <el-option label="保留60天" value="保留60天" />
                <el-option label="保留120天" value="保留120天" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据集恢复启动规则 -->
        <div class="section-title">数据集恢复启动规则</div>
        <div class="form-section">
          <el-form :model="backupRuleForm" label-width="180px">
            <el-form-item label="请选择恢复至指定时期：">
              <el-date-picker
                v-model="backupRuleForm.recoveryToTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="请选择恢复执行时间：">
              <el-date-picker
                v-model="backupRuleForm.recoveryExecutionTime"
                type="datetime"
                placeholder="请选择日期时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 数据集合规性检查弹窗 -->
    <Dialog
      v-model="showComplianceCheckDialog"
      title="数据集合规性检查"
      width="1100px"
      :destroy-on-close="true"
      confirm-text="保存配置"
      cancel-text="关闭"
      @click-confirm="saveComplianceConfig"
      @click-cancel="showComplianceCheckDialog = false"
    >
      <div class="compliance-check" style="padding: 20px; min-height: 400px;">
        <!-- 合规性检查规则管理 -->
        <div class="section">
          <div class="section-title">合规性检查规则管理</div>

          <!-- 操作按钮 -->
          <div class="operation-buttons" style="margin-bottom: 20px;">
            <el-button type="primary" @click="showAddRuleDialog = true">添加规则</el-button>
          </div>

          <!-- 规则列表表格 -->
          <el-table :data="ruleList" style="width: 100%" border>
            <el-table-column label="序号" width="80" align="center">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="ruleName" label="规则名称" width="150" />
            <el-table-column prop="applicableField" label="适用字段" width="120" />
            <el-table-column prop="ruleType" label="规则类型" width="120" />
            <el-table-column prop="violationHandling" label="违规处理方式" width="120" />
            <el-table-column prop="ruleStatus" label="规则状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.ruleStatus === '启用' ? 'success' : 'danger'">
                  {{ row.ruleStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="150" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditRule(row)">编辑</el-button>
                <el-popconfirm title="确认删除这条规则吗？" @confirm="handleDeleteRule(row)">
                  <template #reference>
                    <el-button type="danger" size="small">删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 合规性检查配置 -->
        <div class="section" style="margin-top: 30px;">
          <div class="section-title">合规性检查配置</div>
          <el-form :model="checkConfigForm" label-width="120px" style="max-width: 600px;">
            <el-form-item label="检查频率：">
              <el-select v-model="checkConfigForm.frequency" style="width: 200px;">
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
              </el-select>
            </el-form-item>
            <el-form-item label="执行时间：">
              <el-time-picker
                v-model="checkConfigForm.executionTime"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                placeholder="请选择执行时间"
                style="width: 200px;"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 添加规则弹窗 -->
    <Dialog
      v-model="showAddRuleDialog"
      :title="currentRule ? '编辑规则' : '添加规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="ruleFormLoading"
      loading-text="保存中"
      confirm-text="保存"
      cancel-text="取消"
      @click-confirm="handleSaveRule"
      @click-cancel="showAddRuleDialog = false; resetRuleForm()"
    >
      <el-form :model="ruleForm" label-width="120px">
        <el-form-item label="规则名称：" required>
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="适用字段：" required>
          <el-select v-model="ruleForm.applicableField" placeholder="请选择适用字段" style="width: 100%">
            <el-option label="数据集名称" value="数据集名称" />
            <el-option label="数据集类型" value="数据集类型" />
            <el-option label="数据集分类" value="数据集分类" />
            <el-option label="数据源名称" value="数据源名称" />
            <el-option label="更新时间" value="更新时间" />
            <el-option label="创建人" value="创建人" />
            <el-option label="描述" value="描述" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则类型：" required>
          <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
            <el-option label="格式校验" value="格式校验" />
            <el-option label="范围校验" value="范围校验" />
            <el-option label="必填校验" value="必填校验" />
            <el-option label="自定义校验" value="自定义校验" />
          </el-select>
        </el-form-item>
        <el-form-item label="违规处理方式：" required>
          <el-select v-model="ruleForm.violationHandling" placeholder="请选择违规处理方式" style="width: 100%">
            <el-option label="仅告警" value="仅告警" />
            <el-option label="阻止记录" value="阻止记录" />
            <el-option label="自动修复" value="自动修复" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则状态：" required>
          <el-select v-model="ruleForm.ruleStatus" placeholder="请选择规则状态" style="width: 100%">
            <el-option label="启用" value="启用" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则描述：">
          <el-input
            v-model="ruleForm.ruleDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述"
          />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 数据校验弹窗 -->
    <Dialog
      v-model="showDataValidationDialog"
      title="数据集校验"
      width="700px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDataValidationDialog = false"
    >
      <div class="rule-config" style="padding: 20px; min-height: 500px;">
        <!-- 数据集校验 -->
        <div class="section">
          <div class="section-title">数据集校验</div>
          <div class="form-section">
            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">校验维度</label>
              <div class="checkbox-group" style="display: flex; gap: 20px;">
                <el-checkbox v-model="validationForm.completeness">完整性（非空校验）</el-checkbox>
                <el-checkbox v-model="validationForm.accuracy">准确性（格式校验）</el-checkbox>
                <el-checkbox v-model="validationForm.uniqueness">唯一性（数据集名称重复校验）</el-checkbox>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据脱敏 -->
        <div class="section" style="margin-top: 30px;">
          <div class="section-title">数据脱敏</div>
          <div class="form-section">
            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">
                <span style="color: red;">*</span> 脱敏方法：
              </label>
              <el-select v-model="validationForm.desensitizationMethod" placeholder="请选择" style="width: 100%;">
                <el-option label="部分遮蔽" value="partial_mask" />
                <el-option label="哈希加密" value="hash_encrypt" />
                <el-option label="数据替换" value="data_replace" />
                <el-option label="置空" value="nullify" />
              </el-select>
            </div>

            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">
                <span style="color: red;">*</span> 是否启动：
              </label>
              <el-switch v-model="validationForm.desensitizationEnabled" />
            </div>
          </div>
        </div>

        <!-- 数据去重 -->
        <div class="section" style="margin-top: 30px;">
          <div class="section-title">数据去重</div>
          <div class="form-section">
            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">
                <span style="color: red;">*</span> 去重类型：
              </label>
              <div style="display: flex; gap: 20px;">
                <el-radio v-model="validationForm.deduplicationType" label="exact">精确去重（所有字段值一致）</el-radio>
                <el-radio v-model="validationForm.deduplicationType" label="key_fields">关键字段去重（根据业务主键去重）</el-radio>
              </div>
            </div>

            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">
                <span style="color: red;">*</span> 重复记录处理方式：
              </label>
              <div style="display: flex; flex-direction: column; gap: 10px;">
                <el-radio v-model="validationForm.duplicateHandling" label="delete_first">删除重复项，保留第一条</el-radio>
                <el-radio v-model="validationForm.duplicateHandling" label="mark_only">标记重复项，不删除</el-radio>
                <el-radio v-model="validationForm.duplicateHandling" label="merge_latest">合并重复项，保留最新数据</el-radio>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据更新频率设置 -->
        <div class="section" style="margin-top: 30px;">
          <div class="section-title">数据更新频率设置</div>
          <div class="form-section">
            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">执行频率</label>
              <div style="display: flex; align-items: center; gap: 15px;">
                <el-select v-model="validationForm.frequency" placeholder="请选择" style="width: 150px;">
                  <el-option label="每日" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="每月" value="monthly" />
                </el-select>

                <span>时分秒</span>
                <el-time-picker
                  v-model="validationForm.executionTime"
                  format="HH:mm:ss"
                  placeholder="选择时间"
                  style="width: 150px;"
                />
              </div>
            </div>

            <div class="form-item" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 8px;">
                <span style="color: red;">*</span> 是否启动：
              </label>
              <el-switch v-model="validationForm.frequencyEnabled" />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDataValidationDialog = false">取消</el-button>
          <el-button type="primary" @click="saveDataValidationConfig">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据备份与恢复验证弹窗 -->
    <Dialog
      v-model="showBackupVerifyDialog"
      title="数据集备份与恢复验证"
      :destroy-on-close="true"
      width="600px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <el-form :model="backupVerifyForm" label-width="80px">
          <el-form-item label="类型：">
            <el-select v-model="backupVerifyForm.type" style="width: 100%">
              <el-option label="数据备份验证" value="数据备份验证" />
              <el-option label="数据恢复验证" value="数据恢复验证" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据集：">
            <el-select v-model="backupVerifyForm.dataSource" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in dataSetList"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>

          <!-- 恢复点字段，仅在数据恢复验证时显示 -->
          <el-form-item v-if="backupVerifyForm.type === '数据恢复验证'" label="恢复点：">
            <el-date-picker
              v-model="backupVerifyForm.recoveryPoint"
              type="datetime"
              placeholder="请选择年月日"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-form>

        <!-- 验证按钮 -->
        <div style="text-align: center; margin: 20px 0;">
          <el-button
            type="primary"
            style="width: 100%;"
            @click="executeBackupVerify"
          >
            {{ backupVerifyForm.type === '数据备份验证' ? '验证备份' : '验证恢复' }}
          </el-button>
        </div>

        <!-- 验证结果 -->
        <div v-if="verifyResult" style="margin-top: 20px; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
          <div style="color: #1890ff; font-weight: bold; margin-bottom: 8px;">验证结果：</div>
          <div style="color: #333;">{{ verifyResult }}</div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBackupVerifyDialog = false">关闭</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据安全性验证弹窗 -->
    <Dialog
      v-model="showSecurityVerificationDialog"
      title="数据安全性验证与规则"
      width="1000px"
      :destroy-on-close="true"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="showSecurityVerificationDialog = false"
      @click-cancel="showSecurityVerificationDialog = false"
    >
      <div class="security-verification" style="padding: 20px; min-height: 400px;">
        <!-- 操作按钮 -->
        <div class="operation-buttons" style="margin-bottom: 20px;">
          <el-button type="primary" @click="showAddSecurityRuleDialog = true">新增</el-button>
        </div>

        <!-- 规则列表表格 -->
        <el-table :data="securityRuleList" style="width: 100%" border>
          <el-table-column label="序号" width="80" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="ruleName" label="规则名称" width="150" />
          <el-table-column prop="ruleType" label="规则类型" width="200" />
          <el-table-column prop="ruleDescription" label="规则描述" />
          <el-table-column label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-button type="danger" size="small" @click="handleDeleteSecurityRule(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </Dialog>

    <!-- 添加安全规则弹窗 -->
    <Dialog
      v-model="showAddSecurityRuleDialog"
      :title="currentSecurityRule ? '编辑规则' : '新增规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="securityRuleFormLoading"
      loading-text="保存中"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="handleSaveSecurityRule"
      @click-cancel="showAddSecurityRuleDialog = false; resetSecurityRuleForm()"
    >
      <div style="padding: 20px;">
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">规则名称</label>
          <el-input v-model="securityRuleForm.ruleName" placeholder="请输入" />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">规则描述</label>
          <el-input v-model="securityRuleForm.ruleDescription" placeholder="请输入" />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">规则类型</label>
          <div style="display: flex; flex-direction: column; gap: 10px;">
            <el-radio v-model="securityRuleForm.ruleType" label="去除空白">去除空白</el-radio>
            <div v-if="securityRuleForm.ruleType === '日期格式化'" style="margin-left: 24px;">
              <el-input
                v-model="securityRuleForm.dateFormat"
                placeholder="请输入目标日期格式（例如：YYYY-MM-DD）"
                style="width: 300px;"
              />
            </div>
            <el-radio v-model="securityRuleForm.ruleType" label="日期格式化">日期格式化</el-radio>
            <div v-if="securityRuleForm.ruleType === '替换字符'" style="margin-left: 24px; display: flex; gap: 10px;">
              <el-input
                v-model="securityRuleForm.replaceFrom"
                placeholder="请输入需要替换的字符"
                style="width: 150px;"
              />
              <el-input
                v-model="securityRuleForm.replaceTo"
                placeholder="请输入需要替换的字符"
                style="width: 150px;"
              />
            </div>
            <el-radio v-model="securityRuleForm.ruleType" label="替换字符">替换字符</el-radio>
            <div v-if="securityRuleForm.ruleType === '正则表达式'" style="margin-left: 24px;">
              <el-input
                v-model="securityRuleForm.regexPattern"
                placeholder="请输入正则"
                style="width: 300px;"
              />
            </div>
            <el-radio v-model="securityRuleForm.ruleType" label="正则表达式">正则表达式</el-radio>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 安全性加固设置弹窗 -->
    <Dialog
      v-model="showSecurityHardeningDialog"
      title="数据安全加固规则"
      width="1000px"
      :destroy-on-close="true"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="showSecurityHardeningDialog = false"
      @click-cancel="showSecurityHardeningDialog = false"
    >
      <div class="security-hardening" style="padding: 20px; min-height: 400px;">
        <!-- 操作按钮 -->
        <div class="operation-buttons" style="margin-bottom: 20px;">
          <el-button type="primary" @click="showAddHardeningRuleDialog = true">添加规则</el-button>
        </div>

        <!-- 规则列表表格 -->
        <el-table :data="securityHardeningRuleList" style="width: 100%" border>
          <el-table-column label="序号" width="80" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="ruleName" label="规则名称" width="150" />
          <el-table-column prop="ruleType" label="规则类型" width="120" />
          <el-table-column prop="dataSetName" label="数据集名称" />
          <el-table-column prop="status" label="状态" width="80" align="center" />
          <el-table-column label="操作" width="150" align="center">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEditHardeningRule(row)">修改</el-button>
              <el-button type="danger" size="small" @click="handleDeleteHardeningRule(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </Dialog>

    <!-- 添加安全加固规则弹窗 -->
    <Dialog
      v-model="showAddHardeningRuleDialog"
      :title="currentHardeningRule ? '编辑规则' : '添加规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="hardeningRuleFormLoading"
      loading-text="保存中"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="handleSaveHardeningRule"
      @click-cancel="showAddHardeningRuleDialog = false; resetHardeningRuleForm()"
    >
      <div style="padding: 20px;">
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">规则名称</label>
          <el-input v-model="hardeningRuleForm.ruleName" placeholder="请输入" />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">规则类型</label>
          <el-select v-model="hardeningRuleForm.ruleType" style="width: 100%">
            <el-option label="数据加密" value="数据加密" />
            <el-option label="访问控制" value="访问控制" />
            <el-option label="内容过滤" value="内容过滤" />
          </el-select>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">数据集</label>
          <el-select v-model="hardeningRuleForm.dataSetName" style="width: 100%" multiple>
            <el-option
              v-for="item in dataSetList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>

        <!-- 数据加密相关字段 -->
        <template v-if="hardeningRuleForm.ruleType === '数据加密'">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">加密算法</label>
            <el-select v-model="hardeningRuleForm.encryptionAlgorithm" style="width: 100%">
              <el-option label="AES-256" value="AES-256" />
              <el-option label="RSA-2048" value="RSA-2048" />
            </el-select>
          </div>

          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">加密字段</label>
            <el-select v-model="hardeningRuleForm.encryptionFields" style="width: 100%" multiple>
              <el-option label="所属城市" value="所属城市" />
              <el-option label="所属区县" value="所属区县" />
              <el-option label="所属街道" value="所属街道" />
              <el-option label="所属社区" value="所属社区" />
              <el-option label="更新时间" value="更新时间" />
              <el-option label="填报人" value="填报人" />
              <el-option label="编辑人" value="编辑人" />
              <el-option label="数据来源" value="数据来源" />
            </el-select>
          </div>
        </template>

        <!-- 访问控制相关字段 -->
        <template v-if="hardeningRuleForm.ruleType === '访问控制'">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">访问控制级别</label>
            <el-input v-model="hardeningRuleForm.accessControlLevel" placeholder="请输入访问控制级别" />
          </div>
        </template>

        <!-- 内容过滤相关字段 -->
        <template v-if="hardeningRuleForm.ruleType === '内容过滤'">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">过滤规则</label>
            <el-input v-model="hardeningRuleForm.contentFilterRules" placeholder="请输入内容过滤规则" />
          </div>
        </template>
      </div>
    </Dialog>

    <!-- 数据集日志管理弹窗 -->
    <Dialog
      v-model="showLogDialog"
      title="数据集日志管理"
      :destroy-on-close="true"
      width="700px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showLogDialog = false"
    >
      <div class="log-dialog-content">
        <!-- 搜索区域 -->
        <div class="log-search">
          <el-form :model="logSearchForm" inline>
            <el-form-item label="数据集">
              <el-input v-model="logSearchForm.dataSource" placeholder="请输入数据集" size="small" style="width: 200px" clearable />
            </el-form-item>
            <el-form-item label="操作时间">
              <el-input v-model="logSearchForm.operationTime" placeholder="请输入时间" size="small" style="width: 200px" clearable />
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="onLogSearch">查询</el-button>
              <el-button size="small" @click="onLogReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 日志表格 -->
        <div class="log-table">
          <el-table :data="getFilteredLogs()" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="65" align="center" />
            <el-table-column prop="operatorUser" label="操作用户" width="120" align="center" />
            <el-table-column prop="operationType" label="操作类型" width="120" align="center">
              <template #default="scope">
                <el-tag
                  :type="scope.row.operationType === '查看' ? 'info' :
                        scope.row.operationType === '新增' ? 'success' :
                        scope.row.operationType === '编辑' ? 'warning' : 'danger'"
                >
                  {{ scope.row.operationType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operationDataSource" label="操作数据集" width="200" align="center" />
            <el-table-column prop="operationTime" label="操作时间" width="150" align="center" />
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 异常处理弹窗 -->
    <Dialog
      v-model="showExceptionHandlingDialog"
      title="数据集异常处理"
      width="1000px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
    >
      <div class="exception-dialog-content">
        <el-table :data="exceptionList" style="width: 100%">
          <el-table-column prop="id" label="序号" width="80" />
          <el-table-column prop="dataSetName" label="数据集名称" width="120" />
          <el-table-column prop="exceptionType" label="异常类型" width="120" />
          <el-table-column prop="description" label="描述" width="200" />
          <el-table-column prop="occurTime" label="发生时间" width="150" />
          <el-table-column prop="status" label="处理状态" width="100">
            <template #default="scope">
              <span :style="{ color: scope.row.status === '已处理' ? '#67C23A' : '#F56C6C' }">
                {{ scope.row.status }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button
                v-if="scope.row.status === '未处理'"
                type="primary"
                link
                @click="handleException(scope.row)"
              >
                处理
              </el-button>
              <el-button
                v-else
                type="primary"
                link
                @click="viewException(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="showExceptionHandlingDialog = false">取消</el-button>
        </span>
      </template>
    </Dialog>

    <!-- 异常处理详情弹窗 -->
    <Dialog
      v-model="showExceptionDetailDialog"
      title="数据集异常处理"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="exceptionDetailMode === 'edit'"
      confirm-text="确定"
      @click-confirm="saveExceptionProcessing"
    >
      <div class="exception-detail-content" v-if="currentException">
        <div class="detail-row" style="margin-bottom: 15px;">
          <label style="font-weight: bold; margin-right: 10px;">数据集名称：</label>
          <span>{{ currentException.dataSetName }}</span>
        </div>

        <div class="detail-row" style="margin-bottom: 15px;">
          <label style="font-weight: bold; margin-right: 10px;">异常类型：</label>
          <span>{{ currentException.exceptionType }}</span>
        </div>

        <div class="detail-row" style="margin-bottom: 15px;">
          <label style="font-weight: bold; margin-right: 10px;">描述：</label>
          <span>{{ currentException.description }}</span>
        </div>

        <div class="detail-row" style="margin-bottom: 15px;">
          <label style="font-weight: bold; margin-right: 10px;">发生时间：</label>
          <span>{{ currentException.occurTime }}</span>
        </div>

        <div class="detail-row" style="margin-bottom: 15px;">
          <label style="font-weight: bold; margin-right: 10px;">处理状态：</label>
          <span :style="{ color: currentException.status === '已处理' ? '#67C23A' : '#F56C6C' }">
            {{ currentException.status }}
          </span>
        </div>

        <div class="detail-row">
          <label style="font-weight: bold; margin-right: 10px;">处理描述：</label>
          <el-input
            v-if="exceptionDetailMode === 'edit'"
            v-model="processingDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入处理描述"
          />
          <span v-else>{{ currentException.processingDescription || '暂无处理描述' }}</span>
        </div>
      </div>
    </Dialog>

    <!-- 性能管理与监控弹窗 -->
    <Dialog
      v-model="showPerformanceMonitorDialog"
      title="数据集性能管理与监控"
      width="1200px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
    >
      <div class="performance-monitor-content" style="padding: 20px;">
        <!-- 性能指标卡片 -->
        <div class="performance-cards" style="display: flex; gap: 20px; margin-bottom: 30px;">
          <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #409eff;">{{ performanceData.totalConnections }}</div>
            <div style="color: #909399; margin-top: 5px;">总连接数</div>
            <div style="color: #67c23a; font-size: 12px; margin-top: 5px;">↑ 实时监控</div>
          </div>
          <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #e6a23c;">{{ performanceData.responseTime }}</div>
            <div style="color: #909399; margin-top: 5px;">响应时间</div>
            <div style="color: #67c23a; font-size: 12px; margin-top: 5px;">↑ 实时监控</div>
          </div>
          <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #67c23a;">{{ performanceData.throughput }}</div>
            <div style="color: #909399; margin-top: 5px;">吞吐量</div>
            <div style="color: #67c23a; font-size: 12px; margin-top: 5px;">↑ 实时监控</div>
          </div>
          <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #f56c6c;">{{ performanceData.cpuUsage }}</div>
            <div style="color: #909399; margin-top: 5px;">CPU使用率</div>
            <div style="color: #67c23a; font-size: 12px; margin-top: 5px;">↑ 实时监控</div>
          </div>
        </div>

        <!-- 性能趋势图 -->
        <div class="performance-chart" style="margin-bottom: 30px;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px;">性能趋势</div>
          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <el-button
              :type="activeChartType === 'throughput' ? 'primary' : ''"
              size="small"
              @click="switchChartType('throughput')"
            >
              数据吞吐量
            </el-button>
            <el-button
              :type="activeChartType === 'response' ? 'primary' : ''"
              size="small"
              @click="switchChartType('response')"
            >
              查询响应
            </el-button>
            <el-button
              :type="activeChartType === 'connections' ? 'primary' : ''"
              size="small"
              @click="switchChartType('connections')"
            >
              连接数
            </el-button>
          </div>
          <div style="height: 300px; border: 1px solid #e4e7ed; border-radius: 8px; padding: 20px; background: #f8f9fa;">
            <!-- ECharts 图表容器 -->
            <div ref="performanceChartRef" style="width: 100%; height: 100%;"></div>
          </div>
        </div>

        <!-- 数据集性能详情 -->
        <div class="performance-table">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px;">数据集性能详情</div>
          <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <el-select placeholder="按数据集类型筛选" size="small" style="width: 200px;">
              <el-option label="全部" value="" />
              <el-option label="结构化数据" value="structured" />
              <el-option label="半结构化数据" value="semi-structured" />
            </el-select>
            <el-button type="primary" size="small">
              <el-icon><Search /></el-icon>
            </el-button>
          </div>
          <el-table :data="performanceTableData" style="width: 100%" border>
            <el-table-column prop="name" label="数据集名称" min-width="200" />
            <el-table-column prop="size" label="大小" min-width="100" />
            <el-table-column prop="readWrite" label="读写响应时间" min-width="150">
              <template #default="scope">
                <span v-html="scope.row.readWrite"></span>
              </template>
            </el-table-column>
            <el-table-column prop="throughput" label="吞吐量" min-width="150">
              <template #default="scope">
                <span v-html="scope.row.throughput"></span>
              </template>
            </el-table-column>
            <el-table-column prop="cpuUsage" label="CPU使用率" min-width="120">
              <template #default="scope">
                <span v-html="scope.row.cpuUsage"></span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPerformanceMonitorDialog = false">取消</el-button>
          <el-button type="primary" @click="showPerformanceMonitorDialog = false">确定</el-button>
        </span>
      </template>
    </Dialog>

    <!-- 数据归档管理弹窗 -->
    <Dialog
      v-model="showArchiveManagementDialog"
      title="数据集数据归档管理"
      width="1200px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
    >
      <div class="archive-management-content" style="padding: 20px;">
        <!-- Tab 切换 -->
        <div class="archive-tabs" style="margin-bottom: 20px;">
          <el-button
            :type="activeArchiveTab === 'archiveManagement' ? 'primary' : ''"
            @click="activeArchiveTab = 'archiveManagement'"
          >
            归档管理
          </el-button>
          <el-button
            :type="activeArchiveTab === 'archiveRules' ? 'primary' : ''"
            @click="activeArchiveTab = 'archiveRules'"
          >
            归档规则配置
          </el-button>
        </div>

        <!-- 归档管理 -->
        <div v-if="activeArchiveTab === 'archiveManagement'" class="archive-management">
          <!-- 统计卡片 -->
          <div class="stats-cards" style="display: flex; gap: 20px; margin-bottom: 30px;">
            <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #409eff;">{{ archiveStats.count }}</div>
              <div style="color: #909399; margin-top: 5px;">已归档数据</div>
            </div>
            <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #e6a23c;">{{ archiveStats.totalSize }}</div>
              <div style="color: #909399; margin-top: 5px;">归档存储空间</div>
            </div>
            <div class="card" style="flex: 1; padding: 20px; border: 1px solid #e4e7ed; border-radius: 8px; text-align: center;">
              <div style="font-size: 24px; font-weight: bold; color: #67c23a;">{{ archiveStats.latestTime }}</div>
              <div style="color: #909399; margin-top: 5px;">最近归档时间</div>
            </div>
          </div>

          <!-- 归档历史记录 -->
          <div class="archive-history">
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px;">归档历史记录</div>
            <el-table :data="archiveList" style="width: 100%" border>
              <el-table-column type="index" label="序号" width="80" :index="(index) => index + 1" />
              <el-table-column prop="dataSetName" label="数据集名称" min-width="200" />
              <el-table-column prop="archiveTime" label="归档时间" min-width="150" />
              <el-table-column prop="size" label="大小" min-width="100" />
              <el-table-column prop="location" label="存储位置" min-width="150" />
              <el-table-column label="操作" min-width="100">
                <template #default="scope">
                  <el-button type="primary" link @click="viewArchiveDetail(scope.row)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 归档规则配置 -->
        <div v-if="activeArchiveTab === 'archiveRules'" class="archive-rules">
          <div class="rules-header" style="margin-bottom: 20px;">
            <el-button type="primary" @click="showAddArchiveRuleDialog = true">添加归档规则配置</el-button>
          </div>

          <el-table :data="archiveRuleList" style="width: 100%" border>
            <el-table-column type="index" label="序号" width="80" :index="(index) => index + 1" />
            <el-table-column prop="ruleName" label="规则名称" min-width="150" />
            <el-table-column prop="applicableDataSets" label="适用数据集" min-width="200" />
            <el-table-column prop="archiveCondition" label="归档条件" min-width="150" />
            <el-table-column prop="executionPeriod" label="执行周期" min-width="120" />
            <el-table-column prop="archiveLocation" label="归档存储位置" min-width="150" />
            <el-table-column prop="status" label="状态" min-width="100">
              <template #default="scope">
                <el-button
                  v-if="scope.row.status"
                  :type="scope.row.status === '启用' ? 'success' : 'danger'"
                  size="small"
                  @click="toggleArchiveRuleStatus(scope.row)"
                >
                  {{ scope.row.status }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" link @click="editArchiveRule(scope.row)">修改</el-button>
                <el-popconfirm title="确认删除这条规则吗？" @confirm="deleteArchiveRule(scope.row)">
                  <template #reference>
                    <el-button type="danger" link>删除</el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showArchiveManagementDialog = false">取消</el-button>
          <el-button type="primary" @click="showArchiveManagementDialog = false">确定</el-button>
        </span>
      </template>
    </Dialog>

    <!-- 添加归档规则配置弹窗 -->
    <Dialog
      v-model="showAddArchiveRuleDialog"
      title="数据集数据归档管理"
      width="800px"
      :destroy-on-close="true"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="saveArchiveRule"
      @click-cancel="showAddArchiveRuleDialog = false; resetArchiveRuleForm()"
    >
      <div class="archive-rule-form" style="padding: 20px;">
        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 规则名称</label>
          <el-input v-model="archiveRuleForm.ruleName" placeholder="请输入" />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 规则描述</label>
          <el-input v-model="archiveRuleForm.ruleDescription" placeholder="请输入" />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 适用数据集</label>
          <el-select
            v-model="archiveRuleForm.applicableDataSets"
            placeholder="请选择（从数据集列表中选择）"
            style="width: 100%"
            multiple
          >
            <el-option
              v-for="item in dataSetList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 归档条件</label>
          <div v-for="(condition, index) in archiveRuleForm.archiveConditions" :key="index" style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
            <el-select v-model="condition.field" style="width: 150px;">
              <el-option label="数据创建时间" value="数据创建时间" />
              <el-option label="数据大小" value="数据大小" />
              <el-option label="访问频率" value="访问频率" />
            </el-select>
            <el-select v-model="condition.operator" style="width: 120px;">
              <el-option label="大于" value="大于" />
              <el-option label="小于" value="小于" />
              <el-option label="等于" value="等于" />
              <el-option label="小于等于" value="小于等于" />
              <el-option label="大于等于" value="大于等于" />
            </el-select>
            <el-input v-model="condition.value" placeholder="请输入" style="width: 100px;" />
            <el-select v-model="condition.unit" style="width: 100px;">
              <el-option v-if="condition.field === '数据创建时间'" label="天" value="天" />
              <el-option v-if="condition.field === '数据创建时间'" label="周" value="周" />
              <el-option v-if="condition.field === '数据创建时间'" label="月" value="月" />
              <el-option v-if="condition.field === '数据创建时间'" label="年" value="年" />
              <el-option v-if="condition.field === '访问频率'" label="天" value="天" />
              <el-option v-if="condition.field === '访问频率'" label="周" value="周" />
              <el-option v-if="condition.field === '访问频率'" label="月" value="月" />
              <el-option v-if="condition.field === '数据大小'" label="GB" value="GB" />
              <el-option v-if="condition.field === '数据大小'" label="TB" value="TB" />
            </el-select>
            <el-button type="danger" @click="removeArchiveCondition(index)">删除</el-button>
          </div>
          <el-button type="primary" @click="addArchiveCondition">添加归档条件</el-button>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 执行周期</label>
          <el-select v-model="archiveRuleForm.executionPeriod" placeholder="请选择" style="width: 100%;">
            <el-option label="请选择" value="" />
            <el-option label="每天" value="每天" />
            <el-option label="每周" value="每周" />
            <el-option label="每月" value="每月" />
            <el-option label="每季度" value="每季度" />
            <el-option label="每半年" value="每半年" />
            <el-option label="一次性" value="一次性" />
          </el-select>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 执行时间</label>
          <!-- 当执行周期为每天或一次性时，选择时分秒 -->
          <el-time-picker
            v-if="archiveRuleForm.executionPeriod === '每天' || archiveRuleForm.executionPeriod === '一次性'"
            v-model="archiveRuleForm.executionTime"
            placeholder="请选择（时分秒）"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            style="width: 100%;"
          />
          <!-- 其他执行周期选择日期 -->
          <el-date-picker
            v-else
            v-model="archiveRuleForm.executionTime"
            type="date"
            placeholder="请选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%;"
          />
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;"><span style="color: red;">*</span> 归档存储位置</label>
          <el-select v-model="archiveRuleForm.archiveLocation" placeholder="请选择" style="width: 100%;">
            <el-option label="../data/archiving/1" value="../data/archiving/1" />
            <el-option label="../data/archiving/2" value="../data/archiving/2" />
            <el-option label="../data/archiving/3" value="../data/archiving/3" />
            <el-option label="../data/archiving/4" value="../data/archiving/4" />
          </el-select>
        </div>
      </div>
    </Dialog>

    <!-- 数据集权限审计弹窗 -->
    <Dialog
      v-model="showPermissionAuditDialog"
      title="数据集权限审计"
      :destroy-on-close="true"
      width="890px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <!-- 统计区域 -->
        <div style="margin-bottom: 30px;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">统计概览</div>
          <div style="display: flex; gap: 30px;">
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ dataSetList.length }}</div>
              <div style="color: #666; margin-top: 5px;">数据集总数</div>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #67C23A;">{{ permissionAuditData.length }}</div>
              <div style="color: #666; margin-top: 5px;">已授权用户数</div>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; min-width: 120px;">
              <div style="font-size: 24px; font-weight: bold; color: #E6A23C;">{{ permissionAuditData.length * 4 }}</div>
              <div style="color: #666; margin-top: 5px;">权限项数</div>
            </div>
          </div>
        </div>

        <!-- 明细列表 -->
        <div>
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">明细</div>
          <el-table :data="permissionAuditData" style="width: 100%" max-height="400">
            <el-table-column prop="dataSetName" label="数据集名称" width="150" />
            <el-table-column prop="objectName" label="用户名称" width="120" />
            <el-table-column label="查看权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.viewPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="编辑权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.editPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="新增权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.addPermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="删除权限" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.deletePermission" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="授权时间" width="150" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPermissionAuditDialog = false">取消</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 数据集访问审计弹窗 -->
    <Dialog
      v-model="showAccessAuditDialog"
      title="数据集访问审计"
      :destroy-on-close="true"
      width="1000px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <!-- 搜索区域 -->
        <div style="margin-bottom: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
          <el-form :model="accessAuditSearchForm" inline>
            <el-form-item label="数据集：">
              <el-select v-model="accessAuditSearchForm.dataSet" placeholder="请选择数据集" style="width: 150px;">
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in dataSetList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="操作类型：">
              <el-select v-model="accessAuditSearchForm.operationType" placeholder="请选择操作类型" style="width: 120px;">
                <el-option label="全部" value="" />
                <el-option label="创建" value="创建" />
                <el-option label="编辑" value="编辑" />
                <el-option label="删除" value="删除" />
                <el-option label="查看" value="查看" />
                <el-option label="导入" value="导入" />
                <el-option label="导出" value="导出" />
              </el-select>
            </el-form-item>
          
            <el-form-item>
              <el-button type="primary" @click="onAccessAuditSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="filteredAccessAuditData" style="width: 100%" max-height="400">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="operationDataSource" label="数据集" width="150" />
          <el-table-column prop="operationType" label="操作类型" width="120" />
          <el-table-column prop="operatorUser" label="操作用户" width="120" />
          <el-table-column prop="accessTime" label="访问时间" width="180" />
          <el-table-column prop="accessUser" label="访问对象" width="150">
            <template #header>
              <span>访问对象</span>
              <el-tooltip content="功能对象/接口对象/数据库对象" placement="top">
                <el-icon style="margin-left: 4px; color: #409EFF;"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="ipAddress" label="IP地址" width="140" />
          <el-table-column prop="operationTime" label="操作时间" width="180" />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAccessAuditDialog = false">取消</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 日志分析与行为监控弹窗 -->
    <Dialog
      v-model="showLogAnalysisDialog"
      title="数据集数据访问日志分析与行为监控"
      :destroy-on-close="true"
      width="1200px"
      :visible-confirm-button="false"
    >
      <div class="dialog-content">
        <!-- 数据访问行为分析表格 -->
        <div style="margin-bottom: 30px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div style="font-size: 16px; font-weight: bold; color: #333;">数据访问行为分析</div>
            <el-button type="primary" size="small">刷新</el-button>
          </div>

          <el-table :data="logAnalysisData" style="width: 100%" border>
            <el-table-column prop="id" label="序号" width="60" />
            <el-table-column prop="logId" label="日志id" min-width="120" />
            <el-table-column prop="dataSetName" label="数据集名称" min-width="150" />
            <el-table-column prop="visitUser" label="访问用户" min-width="100" />
            <el-table-column prop="operationType" label="操作类型" min-width="100" />
            <el-table-column prop="visitTime" label="访问时间" min-width="180" />
            <el-table-column prop="status" label="状态" min-width="150">
              <template #default="scope">
                <span :style="{ color: scope.row.status.includes('失败') ? '#f56c6c' : '#67c23a' }">
                  {{ scope.row.status }}
                </span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div style="margin-top: 20px; text-align: right;">
            <el-pagination
              v-model:current-page="logAnalysisPagination.currentPage"
              v-model:page-size="logAnalysisPagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="logAnalysisPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleLogAnalysisSizeChange"
              @current-change="handleLogAnalysisCurrentChange"
            />
          </div>
        </div>

        <!-- 数据访问日志分析图表区域 -->
        <div style="margin-bottom: 20px;">
          <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333;">数据访问日志分析</div>

          <div style="display: flex; gap: 30px;">
            <!-- 访问次数柱状图 -->
            <div style="flex: 1;">
              <div ref="logAnalysisChartRef" style="width: 100%; height: 300px;"></div>
            </div>

            <!-- 访问类型分布饼图 -->
            <div style="flex: 1;">
              <div ref="logDistributionChartRef" style="width: 100%; height: 300px;"></div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showLogAnalysisDialog = false">关闭</el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts" name="DataSetTab">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { ArrowDown, Upload, Download, UploadFilled, QuestionFilled } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import * as echarts from 'echarts'

// 导入弹窗组件
import DataSetTypeConfigDialog from './DataSetTypeConfigDialog.vue'
import DataSetCategoryDialog from './DataSetCategoryDialog.vue'
import QualityMonitoringAssessmentDialog from './QualityMonitoringAssessmentDialog.vue'
import OperationButtonConfigDialog from './OperationButtonConfigDialog.vue'

// 路由实例
const router = useRouter()

// 数据集类型定义
interface DataSet {
  id: string
  name: string
  description: string
  type: string
  category: string
  status: boolean
  createTime: string
  createUser: string
  dataSize: string
  recordCount: number
  lastUpdateTime: string
  dataSourceName: string
  dataSourceType: string
}

// 响应式数据
const loading = ref(false)
const tableHeight = ref(400)
const currentRow = ref<DataSet | null>(null)
const selectedRows = ref<DataSet[]>([])

// 搜索表单数据

const searchForm = ref({
  name: '',
  dataSourceName: '',
  dataSourceType: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格列配置
const tableColumns = ref([
  { field: 'name', title: '数据集名称', minWidth: 150 },
  { field: 'description', title: '描述', minWidth: 200 },
  { field: 'dataSourceName', title: '数据源名称', minWidth: 150 },
  { field: 'dataSourceType', title: '数据源类型', width: 120 },
  { field: 'type', title: '数据集类型', width: 120 },
  { field: 'category', title: '分类', width: 100 },
  { field: 'dataSize', title: '数据大小', width: 100 },
  { field: 'recordCount', title: '记录数', width: 100 },
  { field: 'status', title: '状态', width: 80 },
  { field: 'createTime', title: '创建时间', width: 150 },
  { field: 'createUser', title: '创建人', width: 100 },
  { field: 'lastUpdateTime', title: '最后更新时间', width: 150 }
])

// 表格操作按钮
const tableButtons = ref([
  { title: '详情', type: 'info', code: 'view', verify: 'true', more: false },
  { title: '修改', type: 'primary', code: 'edit', verify: 'true', more: false },
  { title: '删除', type: 'danger', code: 'delete', verify: 'true', more: false },
  { title: '数据校验', type: 'primary', code: 'dataValidation', verify: 'true', more: true },
  { title: '备份与恢复验证', type: 'primary', code: 'backupVerify', verify: 'true', more: true },
  { title: '合规性检查', type: 'primary', code: 'complianceCheck', verify: 'true', more: true },
  { title: '安全性验证', type: 'primary', code: 'securityVerification', verify: 'true', more: true },
  { title: '安全加固设置', type: 'primary', code: 'securityHardening', verify: 'true', more: true },
  { title: '日志管理', type: 'primary', code: 'logManagement', verify: 'true', more: true },
  { title: '异常处理', type: 'primary', code: 'exceptionHandling', verify: 'true', more: true },
  { title: '性能监控', type: 'primary', code: 'performanceMonitor', verify: 'true', more: true },
  { title: '归档管理', type: 'primary', code: 'archiveManagement', verify: 'true', more: true },
  { title: '权限审计', type: 'primary', code: 'permissionAudit', verify: 'true', more: true },
  { title: '访问审计', type: 'primary', code: 'accessAudit', verify: 'true', more: true },
  { title: '日志分析', type: 'primary', code: 'logAnalysis', verify: 'true', more: true }
])

// 弹窗相关
const showDialogForm = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const dialogForm = ref<Partial<DataSet>>({})

// 弹窗表单配置
const dialogFormProps = computed(() => [
  { label: '数据集名称', prop: 'name', type: 'text', required: true },
  { label: '描述', prop: 'description', type: 'textarea', required: true },
  { label: '数据集类型', prop: 'type', type: 'select', required: true, options: [
    { label: '结构化数据', value: 'structured' },
    { label: '半结构化数据', value: 'semi-structured' },
    { label: '非结构化数据', value: 'unstructured' }
  ]},
  { label: '数据集分类', prop: 'category', type: 'select', required: true, options: [
    { label: '业务数据', value: 'business' },
    { label: '日志数据', value: 'log' },
    { label: '监控数据', value: 'monitor' }
  ]},
  { label: '状态', prop: 'status', type: 'switch', required: true }
])

// 表单验证规则
const dialogFormRules = {
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据集类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择数据集分类', trigger: 'change' }]
}

// 各种弹窗显示状态
const showDataSetTypeConfigDialog = ref(false)
const showDataSetCategoryDialog = ref(false)
const showQualityMonitoringAssessmentDialog = ref(false)
const showOperationButtonConfigDialog = ref(false)

// 访问权限管理相关数据
const showAccessPermissionDialog = ref(false)
const accessPermissionForm = ref({
  dataSet: '',
  objectType: '用户',
  objectSelection: '',
  readPermission: false,
  tablePermission: false,
  fieldPermission: false,
  viewPermission: false,
  editPermission: false,
  addPermission: false,
  deletePermission: false
})

// 用户和角色数据
const userData = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

const roleData = ref([
  { id: 1, name: '系统管理员' },
  { id: 2, name: '普通用户' }
])

// 权限审计数据存储
const permissionAuditData = ref<any[]>([])

// 数据备份与恢复规则弹窗
const showBackupRuleDialog = ref(false)
const backupRuleForm = ref({
  backupStartTime: '',
  fullBackupFrequency: '每小时',
  incrementalBackupFrequency: '每小时',
  dataCleanupPolicy: '保留30天',
  recoveryToTime: '',
  recoveryExecutionTime: ''
})

// 数据备份与恢复验证弹窗
const showBackupVerifyDialog = ref(false)
const backupVerifyForm = ref({
  type: '数据备份验证',
  dataSource: '',
  recoveryPoint: ''
})
const verifyResult = ref('')

// 数据安全性验证弹窗
const showSecurityVerificationDialog = ref(false)
const securityRuleList = ref([
  {
    id: 1,
    ruleName: '去除空白字符',
    ruleType: '去除空白',
    ruleDescription: '移除字符串两端的空白字符',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    ruleName: '日期格式化',
    ruleType: '日期格式化',
    ruleDescription: '将日期统一格式为YYYY-MM-DD',
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    ruleName: '请输入',
    ruleType: '请选择（去除空白/日期格式化）',
    ruleDescription: '数据源2',
    createTime: '2024-01-17 09:15:00'
  }
])

// 添加安全规则弹窗状态
const showAddSecurityRuleDialog = ref(false)
const securityRuleFormLoading = ref(false)
const currentSecurityRule = ref<any>(null)

// 安全规则表单
const securityRuleForm = ref({
  ruleName: '',
  ruleDescription: '',
  ruleType: '去除空白',
  dateFormat: '',
  replaceFrom: '',
  replaceTo: '',
  regexPattern: ''
})

// 安全性加固设置弹窗
const showSecurityHardeningDialog = ref(false)
const securityHardeningRuleList = ref([
  {
    id: 1,
    ruleName: '身份证号加密规则',
    ruleType: '数据加密',
    dataSetName: '用户信息数据库',
    status: '启用',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    ruleName: '身份证号加密规则',
    ruleType: '数据加密',
    dataSetName: '用户信息数据库，订单数据库',
    status: '禁用',
    createTime: '2024-01-16 14:20:00'
  },
  {
    id: 3,
    ruleName: '身份证号加密规则',
    ruleType: '数据加密',
    dataSetName: '用户信息数据库，订单数据库',
    status: '禁用',
    createTime: '2024-01-17 09:15:00'
  }
])

// 添加安全加固规则弹窗状态
const showAddHardeningRuleDialog = ref(false)
const hardeningRuleFormLoading = ref(false)
const currentHardeningRule = ref<any>(null)

// 安全加固规则表单
const hardeningRuleForm = ref({
  ruleName: '',
  ruleType: '数据加密',
  dataSetName: '',
  encryptionAlgorithm: 'AES-256',
  encryptionFields: '',
  accessControlLevel: '',
  contentFilterRules: ''
})

// 日志管理相关数据
interface DataSetLog {
  id: string
  sequence: number
  operatorUser: string
  operationType: string
  operationDataSource: string
  operationTime: string
  // 访问审计扩展字段
  accessTime?: string
  accessUser?: string
  ipAddress?: string
}

const showLogDialog = ref(false)
const logList = ref<DataSetLog[]>([])
const logSearchForm = ref({
  dataSource: '',
  operationTime: ''
})

const LOG_STORAGE_KEY = 'dataSetLogData'

// 异常处理相关数据
const showExceptionHandlingDialog = ref(false)
const exceptionList = ref([
  {
    id: 1,
    dataSetName: '用户数据',
    exceptionType: '数据缺失',
    description: '用户ID 12345 缺少邮箱信息',
    occurTime: '2025.7.11 10:30',
    status: '未处理',
    processingDescription: ''
  },
  {
    id: 2,
    dataSetName: '用户数据',
    exceptionType: '格式错误',
    description: '订单金额字段包含非数字字符',
    occurTime: '2025.7.11 10:30',
    status: '未处理',
    processingDescription: ''
  },
  {
    id: 3,
    dataSetName: '用户数据',
    exceptionType: '重复数据',
    description: '产品SKU P001 重复录入',
    occurTime: '2025.7.11 10:30',
    status: '已处理',
    processingDescription: '已清理重复数据'
  }
])

const showExceptionDetailDialog = ref(false)
const currentException = ref<any>(null)
const exceptionDetailMode = ref<'view' | 'edit'>('view')
const processingDescription = ref('')

// 性能管理与监控相关数据
const showPerformanceMonitorDialog = ref(false)
const activeChartType = ref('throughput')
const performanceChartRef = ref<HTMLDivElement>()
const performanceData = ref({
  totalConnections: 248,
  responseTime: '128ms',
  throughput: '1.2 GB/s',
  cpuUsage: '0.32%'
})

const performanceChartData = ref({
  throughput: [
    { time: '0:00', value: 120 },
    { time: '3:00', value: 115 },
    { time: '6:00', value: 110 },
    { time: '9:00', value: 125 },
    { time: '12:00', value: 160 },
    { time: '15:00', value: 140 },
    { time: '18:00', value: 155 },
    { time: '21:00', value: 130 }
  ],
  response: [
    { time: '0:00', value: 85 },
    { time: '3:00', value: 92 },
    { time: '6:00', value: 78 },
    { time: '9:00', value: 105 },
    { time: '12:00', value: 128 },
    { time: '15:00', value: 95 },
    { time: '18:00', value: 110 },
    { time: '21:00', value: 88 }
  ],
  connections: [
    { time: '0:00', value: 200 },
    { time: '3:00', value: 180 },
    { time: '6:00', value: 165 },
    { time: '9:00', value: 220 },
    { time: '12:00', value: 248 },
    { time: '15:00', value: 235 },
    { time: '18:00', value: 260 },
    { time: '21:00', value: 210 }
  ]
})

const performanceTableData = ref([
  {
    id: 1,
    name: '用户行为分析数据集',
    size: '12.5 GB',
    readWrite: '246 ms ↑ +2%',
    throughput: '945 MB/s ↑ +5%',
    cpuUsage: '0.57% ↑ +3%',
    status: '正常'
  },
  {
    id: 2,
    name: '实时监控数据集',
    size: '8.4 GB',
    readWrite: '87 ms ↑ +8%',
    throughput: '1.2 GB/s ↑ +15%',
    cpuUsage: '0.12% ↓ -10%',
    status: '正常'
  },
  {
    id: 3,
    name: '客户行为数据集',
    size: '24.6 GB',
    readWrite: '156 ms → 0%',
    throughput: '956 MB/s ↑ +8%',
    cpuUsage: '0.65% ↑ +1%',
    status: '正常'
  },
  {
    id: 4,
    name: '产品销售数据集',
    size: '5.3 GB',
    readWrite: '64 ms ↑ +2%',
    throughput: '1.5 GB/s ↑ +10%',
    cpuUsage: '0.08% ↓ -10%',
    status: '正常'
  },
  {
    id: 5,
    name: '日志分析数据集',
    size: '42.3 GB',
    readWrite: '312 ms ↑ +18%',
    throughput: '628 MB/s ↓ +4%',
    cpuUsage: '1.23% ↑ +3%',
    status: '异常'
  }
])

// 数据归档管理相关数据
const showArchiveManagementDialog = ref(false)
const activeArchiveTab = ref('archiveManagement')
const archiveList = ref([
  
])

const archiveRuleList = ref([

])

const showAddArchiveRuleDialog = ref(false)
const archiveRuleForm = ref({
  id: null as number | null,
  ruleName: '',
  ruleDescription: '',
  applicableDataSets: [] as string[],
  archiveConditions: [
    {
      field: '数据创建时间',
      operator: '大于',
      value: '30',
      unit: '天'
    }
  ],
  executionPeriod: '',
  executionTime: '',
  archiveLocation: ''
})

// 权限审计相关数据
const showPermissionAuditDialog = ref(false)

// 日志分析与行为监控相关数据
const showLogAnalysisDialog = ref(false)
const logAnalysisChartRef = ref<HTMLDivElement>()
const logDistributionChartRef = ref<HTMLDivElement>()

// 日志分析完整数据 - 基于日志管理列表数据生成
const logAnalysisFullData = computed(() => {
  // 基于日志管理列表数据生成访问行为分析数据
  const fullData = logList.value.map((log, index) => {
    // 随机生成失败状态（约10%的概率）
    const isFailure = Math.random() < 0.1
    const failureReasons = ['网络异常', '权限不足', '数据库连接超时', '参数错误']
    const randomFailureReason = failureReasons[Math.floor(Math.random() * failureReasons.length)]

    return {
      id: index + 1,
      logId: `LOG-${8000 + index}`,
      dataSetName: log.operationDataSource,
      visitUser: log.operatorUser,
      operationType: log.operationType,
      visitTime: log.operationTime,
      status: isFailure ? `失败（${randomFailureReason}）` : '成功'
    }
  })

  // 更新分页总数
  logAnalysisPagination.total = fullData.length

  return fullData
})

// 日志分析当前页数据
const logAnalysisData = computed(() => {
  const start = (logAnalysisPagination.currentPage - 1) * logAnalysisPagination.pageSize
  const end = start + logAnalysisPagination.pageSize
  return logAnalysisFullData.value.slice(start, end)
})

// 访问次数统计数据
const visitStatsData = ref([
  { date: '7.2', count: 100 },
  { date: '7.3', count: 120 },
  { date: '7.14', count: 220 },
  { date: '7.15', count: 100 },
  { date: '7.16', count: 140 },
  { date: '7.17', count: 100 },
  { date: '7.8', count: 180 }
])

// 访问类型分布数据
const visitTypeData = ref([
  { name: '查询', value: 150, color: '#5470c6' },
  { name: '新增', value: 100, color: '#91cc75' },
  { name: '删除', value: 80, color: '#fac858' }
])

// 日志分析分页数据
const logAnalysisPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 数据集访问审计相关数据
const showAccessAuditDialog = ref(false)
const accessAuditSearchForm = ref({
  dataSet: '',
  operationType: '',
  operatorUser: '',
  startTime: '',
  endTime: ''
})

// 数据集合规性检查弹窗
const showComplianceCheckDialog = ref(false)
const ruleList = ref([
  {
    id: 1,
    ruleName: '数据集名称格式校验',
    applicableField: '数据集名称',
    ruleType: '格式校验',
    violationHandling: '仅告警',
    ruleStatus: '启用',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    ruleName: '更新时间必填',
    applicableField: '更新时间',
    ruleType: '必填校验',
    violationHandling: '阻止记录',
    ruleStatus: '启用',
    createTime: '2024-01-16 14:20:00'
  }
])

// 检查配置表单
const checkConfigForm = ref({
  frequency: '每日',
  executionTime: '02:00:00'
})

// 添加规则弹窗状态
const showAddRuleDialog = ref(false)
const ruleFormLoading = ref(false)
const currentRule = ref<any>(null)

// 规则表单
const ruleForm = ref({
  ruleName: '',
  applicableField: '',
  ruleType: '',
  violationHandling: '',
  ruleStatus: '启用',
  ruleDescription: ''
})

// 数据校验弹窗
const showDataValidationDialog = ref(false)
const validationForm = ref({
  completeness: false,
  accuracy: false,
  uniqueness: false,
  desensitizationMethod: '',
  desensitizationEnabled: false,
  deduplicationType: 'exact',
  duplicateHandling: 'delete_first',
  frequency: 'daily',
  executionTime: '',
  frequencyEnabled: false
})

// 批量导入相关数据
const batchImportDialogVisible = ref(false)
const importFileList = ref<any[]>([])
const importLoading = ref(false)
const selectedFileName = ref('')

// 缓存键
const STORAGE_KEY = 'dataSetManagement_data'

// 模拟数据 - 直接在页面中初始化
const dataSetList = ref<DataSet[]>([
  {
    id: '1',
    name: '客户关系管理数据集',
    description: '包含客户基本信息、联系记录、购买历史等完整CRM数据',
    type: 'structured',
    category: 'business',
    status: true,
    createTime: '2024-01-15 10:30:00',
    createUser: '张建华',
    dataSize: '3.2GB',
    recordCount: 1850000,
    lastUpdateTime: '2024-07-20 14:20:00',
    dataSourceName: 'MySQL客户数据库',
    dataSourceType: 'MySql'
  },
  {
    id: '2',
    name: '应用系统日志数据集',
    description: '生产环境应用服务器日志，包含访问日志、错误日志、性能日志',
    type: 'semi-structured',
    category: 'log',
    status: true,
    createTime: '2024-02-14 09:15:00',
    createUser: '李明',
    dataSize: '8.7GB',
    recordCount: 12500000,
    lastUpdateTime: '2024-07-21 16:45:00',
    dataSourceName: 'Oracle日志数据库',
    dataSourceType: 'Oracle'
  },
  {
    id: '3',
    name: '服务器性能监控数据集',
    description: '服务器CPU、内存、磁盘、网络等性能指标实时监控数据',
    type: 'structured',
    category: 'monitor',
    status: true,
    createTime: '2024-03-13 15:20:00',
    createUser: '王强',
    dataSize: '2.1GB',
    recordCount: 5600000,
    lastUpdateTime: '2024-07-19 11:30:00',
    dataSourceName: 'SQL Server监控库',
    dataSourceType: 'SQL Server'
  },
  {
    id: '4',
    name: '电商交易流水数据集',
    description: '在线商城订单、支付、退款、物流等完整交易链路数据',
    type: 'structured',
    category: 'business',
    status: true,
    createTime: '2024-04-12 16:45:00',
    createUser: '赵丽',
    dataSize: '15.3GB',
    recordCount: 8900000,
    lastUpdateTime: '2024-07-22 09:30:00',
    dataSourceName: '达梦交易数据库',
    dataSourceType: '达梦'
  },
  {
    id: '5',
    name: '智能制造设备数据集',
    description: '工业4.0智能制造设备传感器数据、生产参数、质量检测数据',
    type: 'unstructured',
    category: 'iot',
    status: true,
    createTime: '2024-05-11 11:20:00',
    createUser: '孙伟',
    dataSize: '22.8GB',
    recordCount: 45600000,
    lastUpdateTime: '2024-07-23 15:10:00',
    dataSourceName: 'MongoDB设备数据库',
    dataSourceType: 'MangoDB'
  },
  {
    id: '6',
    name: '大数据用户画像分析集',
    description: '基于多维度用户行为的画像标签、偏好分析、推荐算法训练数据',
    type: 'structured',
    category: 'analytics',
    status: true,
    createTime: '2024-06-10 14:30:00',
    createUser: '周敏',
    dataSize: '28.5GB',
    recordCount: 15600000,
    lastUpdateTime: '2024-07-24 10:15:00',
    dataSourceName: 'Hive数据仓库',
    dataSourceType: 'Hive'
  },
  {
    id: '7',
    name: '金融风控模型数据集',
    description: '银行信贷风险评估、反欺诈检测、合规监管等金融风控数据',
    type: 'structured',
    category: 'finance',
    status: true,
    createTime: '2024-01-09 09:45:00',
    createUser: '吴刚',
    dataSize: '9.2GB',
    recordCount: 3200000,
    lastUpdateTime: '2024-07-25 16:20:00',
    dataSourceName: '华为云GaussDB',
    dataSourceType: 'Huawei GaussDB'
  },
  {
    id: '8',
    name: '医疗健康档案数据集',
    description: '患者电子病历、检查报告、药物处方、健康体检等医疗数据',
    type: 'semi-structured',
    category: 'healthcare',
    status: false,
    createTime: '2024-02-08 13:25:00',
    createUser: '陈静',
    dataSize: '18.6GB',
    recordCount: 2800000,
    lastUpdateTime: '2024-07-18 12:40:00',
    dataSourceName: 'Oracle医疗数据库',
    dataSourceType: 'Oracle'
  },
  {
    id: '9',
    name: '教育在线学习数据集',
    description: '在线教育平台学习行为、课程评价、考试成绩、学习路径等数据',
    type: 'structured',
    category: 'education',
    status: true,
    createTime: '2024-03-07 16:10:00',
    createUser: '刘洋',
    dataSize: '7.4GB',
    recordCount: 6700000,
    lastUpdateTime: '2024-07-17 14:55:00',
    dataSourceName: 'MySQL教育数据库',
    dataSourceType: 'MySql'
  },
  {
    id: '10',
    name: '智慧城市交通数据集',
    description: '城市交通流量、路况信息、公共交通、停车场使用等智慧交通数据',
    type: 'structured',
    category: 'smart_city',
    status: true,
    createTime: '2024-04-06 08:30:00',
    createUser: '黄磊',
    dataSize: '31.2GB',
    recordCount: 89500000,
    lastUpdateTime: '2024-07-16 18:25:00',
    dataSourceName: 'SQL Server交通数据库',
    dataSourceType: 'SQL Server'
  }
])

// 初始化模拟数据（已移至dataSetList初始化中，保留函数用于重置数据）
const initMockData = () => {
  // 数据已在dataSetList初始化时设置，这里只需要保存到缓存
  saveDataToCache()
}

// 缓存操作 - 不再使用，保留函数以防后续需要
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      // 不再从缓存加载数据，使用页面中初始化的数据
      console.log('缓存中有数据，但使用页面初始化的数据')
    }
  } catch (error) {
    console.error('加载数据集数据失败:', error)
  }
}

const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataSetList.value))
  } catch (error) {
    console.error('保存数据集数据失败:', error)
  }
}

// 计算属性
const filteredData = computed(() => {
  let filtered = dataSetList.value

  if (searchForm.value.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.value.name.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceName) {
    filtered = filtered.filter(item =>
      item.dataSourceName.toLowerCase().includes(searchForm.value.dataSourceName.toLowerCase())
    )
  }

  if (searchForm.value.dataSourceType) {
    filtered = filtered.filter(item => item.dataSourceType === searchForm.value.dataSourceType)
  }

  pagination.total = filtered.length
  return filtered
})

const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filteredData.value.slice(start, end)
})

// 事件处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

const onSearch = () => {
  pagination.page = 1
}

const onReset = () => {
  searchForm.value = {
    name: '',
    dataSourceName: '',
    dataSourceType: ''
  }
  pagination.page = 1
}

const onPageChange = (page: number) => {
  pagination.page = page
}

const onSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const onSelectionChange = (selection: DataSet[]) => {
  selectedRows.value = selection
}

// 顶部按钮事件
const onClickAdd = () => {
  dialogMode.value = 'add'
  currentRow.value = null
  dialogForm.value = {}
  showDialogForm.value = true
}

const onClickBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRows.value.map(row => row.id)
    const deletedDataSets = selectedRows.value.map(row => row.name)
    dataSetList.value = dataSetList.value.filter(item => !selectedIds.includes(item.id))

    // 为每个删除的数据集添加操作日志
    deletedDataSets.forEach(name => {
      addOperationLog('删除', name)
    })

    saveDataToCache()
    ElMessage.success('删除成功')
  })
}

// 批量导出
const onClickBatchExport = async () => {
  // 确定要导出的数据：如果有选中数据则导出选中的，否则导出全部数据
  const exportData = selectedRows.value.length > 0 ? selectedRows.value : filteredData.value
  const exportType = selectedRows.value.length > 0 ? '选中' : '全部'

  if (exportData.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据集数据')

    // 设置表头
    const headers = [
      { header: '序号', key: 'id', width: 10 },
      { header: '数据集名称', key: 'name', width: 30 },
      { header: '数据集类型', key: 'type', width: 15 },
      { header: '数据集分类', key: 'category', width: 15 },
      { header: '数据源', key: 'dataSource', width: 20 },
      { header: '状态', key: 'status', width: 10 },
      { header: '描述', key: 'description', width: 30 },
      { header: '创建时间', key: 'createTime', width: 15 },
      { header: '更新时间', key: 'updateTime', width: 15 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据
    exportData.forEach((row, index) => {
      worksheet.addRow({
        id: index + 1,
        name: row.name,
        type: row.type,
        category: row.category,
        dataSource: row.dataSource,
        status: row.status ? '启用' : '停用',
        description: row.description || '',
        createTime: row.createTime,
        updateTime: row.updateTime
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `数据集数据_${exportType}_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 批量导入
const onClickBatchImport = () => {
  batchImportDialogVisible.value = true
  importFileList.value = []
  selectedFileName.value = ''
}

const onClickUpdateLog = () => {
  ElMessage.info('更新日志功能开发中...')
}

const onClickFieldTemplate = () => {
  // 跳转到数据源接入绑定页面（统一入口）
  router.push('/dataSourceAccessBinding')
}

// 新增的弹窗打开事件
const onClickOperationButtonConfig = () => {
  showOperationButtonConfigDialog.value = true
}

const onClickDataSetTypeConfig = () => {
  showDataSetTypeConfigDialog.value = true
}

const onClickDataSetCategory = () => {
  showDataSetCategoryDialog.value = true
}

const onClickQualityMonitoringAssessment = () => {
  showQualityMonitoringAssessmentDialog.value = true
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据集模板')

    // 设置表头
    const headers = [
      { header: '数据集名称', key: 'name', width: 30 },
      { header: '数据集类型', key: 'type', width: 15 },
      { header: '数据集分类', key: 'category', width: 15 },
      { header: '数据源', key: 'dataSource', width: 20 },
      { header: '描述', key: 'description', width: 30 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加示例数据
    worksheet.addRow({
      name: '示例数据集名称',
      type: '结构化数据',
      category: '业务数据',
      dataSource: '示例数据源',
      description: '示例描述信息'
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `数据集导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 处理文件选择（on-change事件）
const handleFileSelect = (file: any, fileList: any[]) => {
  console.log('文件选择事件:', file, fileList)

  // 检查文件类型
  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    // 清理无效文件
    importFileList.value = []
    selectedFileName.value = ''
    return
  }

  // 检查文件大小（限制为10MB）
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    // 清理无效文件
    importFileList.value = []
    selectedFileName.value = ''
    return
  }

  // 保存文件信息
  importFileList.value = [file]
  selectedFileName.value = file.name
  ElMessage.success('文件选择成功，点击上传按钮开始导入')
}



// 处理文件移除
const handleFileRemove = () => {
  importFileList.value = []
  selectedFileName.value = ''
}

// 上传并解析Excel文件
const uploadFile = async () => {
  console.log('开始上传文件:', importFileList.value, selectedFileName.value)

  if (importFileList.value.length === 0 || !selectedFileName.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 获取文件对象，优先使用raw属性
  const file = importFileList.value[0].raw || importFileList.value[0]
  console.log('获取到的文件对象:', file)

  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    return
  }

  importLoading.value = true

  try {
    const buffer = await file.arrayBuffer()
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(buffer)

    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }

    const importData: DataSet[] = []
    const errors: string[] = []
    const validTypes = ['结构化数据', '半结构化数据', '非结构化数据', '流式数据']
    const validCategories = ['业务数据', '日志数据', '监控数据', '分析数据']

    // 从第二行开始读取数据（第一行是表头）
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return // 跳过表头

      const values = row.values as any[]
      const name = values[1]?.toString()?.trim()
      const type = values[2]?.toString()?.trim()
      const category = values[3]?.toString()?.trim()
      const dataSource = values[4]?.toString()?.trim()
      const description = values[5]?.toString()?.trim() || ''

      // 跳过完全空白的行
      if (!name && !type && !category && !dataSource) {
        return
      }

      // 验证必填字段
      if (!name) {
        errors.push(`第${rowNumber}行：数据集名称不能为空`)
        return
      }
      if (!type) {
        errors.push(`第${rowNumber}行：数据集类型不能为空`)
        return
      }
      if (!category) {
        errors.push(`第${rowNumber}行：数据集分类不能为空`)
        return
      }
      if (!dataSource) {
        errors.push(`第${rowNumber}行：数据源不能为空`)
        return
      }

      // 验证字段类型是否有效
      if (!validTypes.includes(type)) {
        errors.push(`第${rowNumber}行：数据集类型"${type}"无效，请使用：${validTypes.join('、')}`)
        return
      }

      // 验证分类是否有效
      if (!validCategories.includes(category)) {
        errors.push(`第${rowNumber}行：数据集分类"${category}"无效，请使用：${validCategories.join('、')}`)
        return
      }

      // 检查数据集名称是否重复
      const existingItem = dataSetList.value.find(item => item.name === name)
      if (existingItem) {
        errors.push(`第${rowNumber}行：数据集名称"${name}"已存在`)
        return
      }

      // 检查导入数据中是否有重复
      const duplicateInImport = importData.find(item => item.name === name)
      if (duplicateInImport) {
        errors.push(`第${rowNumber}行：数据集名称"${name}"在导入数据中重复`)
        return
      }

      const newItem: DataSet = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name,
        type,
        category,
        dataSource,
        status: true,
        description,
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString(),
        updateUser: '当前用户',
        dataSourceType: 'MySql'
      }

      importData.push(newItem)
    })

    if (errors.length > 0) {
      // 限制错误信息显示数量，避免过长
      const displayErrors = errors.slice(0, 10)
      const errorMessage = displayErrors.join('\n') + (errors.length > 10 ? `\n...还有${errors.length - 10}个错误` : '')
      ElMessage.error(`导入失败，发现以下错误：\n${errorMessage}`)
      return
    }

    if (importData.length === 0) {
      ElMessage.warning('没有找到有效的数据，请检查Excel文件内容')
      return
    }

    // 添加到表格数据
    dataSetList.value.push(...importData)
    saveDataToCache()

    ElMessage.success(`成功导入 ${importData.length} 条数据`)
    batchImportDialogVisible.value = false
    importFileList.value = []
    selectedFileName.value = ''

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error(`文件解析失败：${error.message || '请检查文件格式'}`)
  } finally {
    importLoading.value = false
  }
}

const onClickDataSetSync = () => {
  ElMessage.info('数据集同步功能开发中...')
}

// 数据备份与恢复下拉菜单
const handleDataBackupCommand = (command: string) => {
  switch (command) {
    case 'backupRule':
      onClickBackupRule()
      break
    case 'backupVerify':
      ElMessage.info('数据备份与恢复验证功能开发中...')
      break
  }
}

// 数据备份与恢复规则按钮
const onClickBackupRule = () => {
  loadBackupRuleFromCache()
  showBackupRuleDialog.value = true
}

// 加载备份规则配置
const loadBackupRuleFromCache = () => {
  try {
    const cached = localStorage.getItem('dataSetBackupRule')
    if (cached) {
      const config = JSON.parse(cached)
      backupRuleForm.value = {
        backupStartTime: config.backupStartTime || '',
        fullBackupFrequency: config.fullBackupFrequency || '每小时',
        incrementalBackupFrequency: config.incrementalBackupFrequency || '每小时',
        dataCleanupPolicy: config.dataCleanupPolicy || '保留30天',
        recoveryToTime: config.recoveryToTime || '',
        recoveryExecutionTime: config.recoveryExecutionTime || ''
      }
    }
  } catch (error) {
    console.error('加载数据集备份规则失败:', error)
  }
}

// 保存备份规则配置
const saveBackupRuleToCache = () => {
  try {
    localStorage.setItem('dataSetBackupRule', JSON.stringify(backupRuleForm.value))
    ElMessage.success('数据集备份与恢复规则保存成功')
    showBackupRuleDialog.value = false
  } catch (error) {
    console.error('保存数据集备份规则失败:', error)
    ElMessage.error('保存失败')
  }
}

// 合规性检查相关函数
// 打开合规性检查弹窗
const onClickComplianceCheck = () => {
  loadComplianceConfig()
  showComplianceCheckDialog.value = true
}

// 加载合规性检查配置
const loadComplianceConfig = () => {
  try {
    const cached = localStorage.getItem('dataSetComplianceCheck_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.checkConfig) {
        checkConfigForm.value = { ...checkConfigForm.value, ...config.checkConfig }
      }
      if (config.rules) {
        ruleList.value = config.rules
      }
    }
  } catch (error) {
    console.error('加载数据集合规性检查配置失败:', error)
  }
}

// 保存合规性检查配置
const saveComplianceConfig = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetComplianceCheck_data', JSON.stringify(config))

    ElMessage.success('数据集合规性检查配置保存成功')
    showComplianceCheckDialog.value = false
  } catch (error) {
    console.error('保存数据集合规性检查配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 保存规则到缓存
const saveRulesToCache = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetComplianceCheck_data', JSON.stringify(config))
  } catch (error) {
    console.error('保存规则到缓存失败:', error)
  }
}

// 编辑规则
const handleEditRule = (row: any) => {
  currentRule.value = row
  ruleForm.value = { ...row }
  showAddRuleDialog.value = true
}

// 删除规则
const handleDeleteRule = (row: any) => {
  const index = ruleList.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    ruleList.value.splice(index, 1)
    saveRulesToCache()
    ElMessage.success('删除规则成功')
  }
}

// 保存规则
const handleSaveRule = () => {
  ruleFormLoading.value = true

  setTimeout(() => {
    try {
      if (currentRule.value) {
        // 编辑模式
        const index = ruleList.value.findIndex(item => item.id === currentRule.value.id)
        if (index !== -1) {
          ruleList.value[index] = {
            ...ruleForm.value,
            id: currentRule.value.id,
            createTime: currentRule.value.createTime
          }
        }
      } else {
        // 新增模式
        const newRule = {
          ...ruleForm.value,
          id: Date.now(),
          createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
        ruleList.value.push(newRule)
      }

      saveRulesToCache()
      showAddRuleDialog.value = false
      resetRuleForm()
      ElMessage.success(currentRule.value ? '编辑规则成功' : '添加规则成功')
    } catch (error) {
      console.error('保存规则失败:', error)
      ElMessage.error('保存失败')
    } finally {
      ruleFormLoading.value = false
    }
  }, 1000)
}

// 重置规则表单
const resetRuleForm = () => {
  currentRule.value = null
  ruleForm.value = {
    ruleName: '',
    applicableField: '',
    ruleType: '',
    violationHandling: '',
    ruleStatus: '启用',
    ruleDescription: ''
  }
}

// 数据校验相关函数
// 打开数据校验弹窗
const onClickDataValidation = () => {
  loadDataValidationConfig()
  showDataValidationDialog.value = true
}

// 加载数据校验配置
const loadDataValidationConfig = () => {
  try {
    const cached = localStorage.getItem('dataSetValidation_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.validation) {
        validationForm.value = { ...validationForm.value, ...config.validation }
      }
    }
  } catch (error) {
    console.error('加载数据集校验配置失败:', error)
  }
}

// 保存数据校验配置
const saveDataValidationConfig = () => {
  try {
    // 验证必填项
    if (!validationForm.value.desensitizationMethod) {
      ElMessage.error('请选择脱敏方法')
      return
    }

    // 保存到localStorage
    const config = {
      validation: validationForm.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetValidation_data', JSON.stringify(config))

    ElMessage.success('数据集校验配置保存成功')
    showDataValidationDialog.value = false
  } catch (error) {
    console.error('保存数据集校验配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 数据备份与恢复验证相关函数
// 数据备份与恢复验证按钮
const onClickBackupVerify = () => {
  backupVerifyForm.value = {
    type: '数据备份验证',
    dataSource: '',
    recoveryPoint: ''
  }
  verifyResult.value = ''
  showBackupVerifyDialog.value = true
}

// 执行验证备份
const executeBackupVerify = () => {
  const dataSourceName = backupVerifyForm.value.dataSource || 'xxx数据集'
  if (backupVerifyForm.value.type === '数据备份验证') {
    verifyResult.value = `${dataSourceName}备份验证成功`
  } else {
    const recoveryDate = backupVerifyForm.value.recoveryPoint ?
      new Date(backupVerifyForm.value.recoveryPoint).toLocaleDateString('zh-CN').replace(/\//g, '.') :
      '2025.7.12'
    verifyResult.value = `${dataSourceName}在${recoveryDate} 验证恢复成功`
  }
}

// 数据安全性验证相关函数
// 打开数据安全性验证弹窗
const onClickSecurityVerification = () => {
  loadSecurityRulesFromCache()
  showSecurityVerificationDialog.value = true
}

// 加载安全规则
const loadSecurityRulesFromCache = () => {
  try {
    const cached = localStorage.getItem('dataSetSecurityRules_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.rules) {
        securityRuleList.value = config.rules
      }
    }
  } catch (error) {
    console.error('加载数据集安全规则失败:', error)
  }
}

// 保存安全规则到缓存
const saveSecurityRulesToCache = () => {
  try {
    const config = {
      rules: securityRuleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetSecurityRules_data', JSON.stringify(config))
  } catch (error) {
    console.error('保存安全规则到缓存失败:', error)
  }
}

// 编辑安全规则
const handleEditSecurityRule = (row: any) => {
  currentSecurityRule.value = row
  securityRuleForm.value = { ...row }
  showAddSecurityRuleDialog.value = true
}

// 删除安全规则
const handleDeleteSecurityRule = (row: any) => {
  const index = securityRuleList.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    securityRuleList.value.splice(index, 1)
    saveSecurityRulesToCache()
    ElMessage.success('删除规则成功')
  }
}

// 保存安全规则
const handleSaveSecurityRule = () => {
  securityRuleFormLoading.value = true

  setTimeout(() => {
    try {
      if (currentSecurityRule.value) {
        // 编辑模式
        const index = securityRuleList.value.findIndex(item => item.id === currentSecurityRule.value.id)
        if (index !== -1) {
          securityRuleList.value[index] = {
            ...securityRuleForm.value,
            id: currentSecurityRule.value.id,
            createTime: currentSecurityRule.value.createTime
          }
        }
      } else {
        // 新增模式
        const newRule = {
          ...securityRuleForm.value,
          id: Date.now(),
          createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
        securityRuleList.value.push(newRule)
      }

      saveSecurityRulesToCache()
      showAddSecurityRuleDialog.value = false
      resetSecurityRuleForm()
      ElMessage.success(currentSecurityRule.value ? '编辑规则成功' : '添加规则成功')
    } catch (error) {
      console.error('保存安全规则失败:', error)
      ElMessage.error('保存失败')
    } finally {
      securityRuleFormLoading.value = false
    }
  }, 1000)
}

// 重置安全规则表单
const resetSecurityRuleForm = () => {
  currentSecurityRule.value = null
  securityRuleForm.value = {
    ruleName: '',
    ruleDescription: '',
    ruleType: '去除空白',
    dateFormat: '',
    replaceFrom: '',
    replaceTo: '',
    regexPattern: ''
  }
}

// 安全性加固设置相关函数
// 打开安全性加固设置弹窗
const onClickSecurityHardening = () => {
  loadHardeningRulesFromCache()
  showSecurityHardeningDialog.value = true
}

// 加载安全加固规则
const loadHardeningRulesFromCache = () => {
  try {
    const cached = localStorage.getItem('dataSetSecurityHardeningRules_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.rules) {
        securityHardeningRuleList.value = config.rules
      }
    }
  } catch (error) {
    console.error('加载数据集安全加固规则失败:', error)
  }
}

// 保存安全加固规则到缓存
const saveHardeningRulesToCache = () => {
  try {
    const config = {
      rules: securityHardeningRuleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('dataSetSecurityHardeningRules_data', JSON.stringify(config))
  } catch (error) {
    console.error('保存安全加固规则到缓存失败:', error)
  }
}

// 编辑安全加固规则
const handleEditHardeningRule = (row: any) => {
  currentHardeningRule.value = row
  hardeningRuleForm.value = { ...row }
  showAddHardeningRuleDialog.value = true
}

// 删除安全加固规则
const handleDeleteHardeningRule = (row: any) => {
  const index = securityHardeningRuleList.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    securityHardeningRuleList.value.splice(index, 1)
    saveHardeningRulesToCache()
    ElMessage.success('删除规则成功')
  }
}

// 保存安全加固规则
const handleSaveHardeningRule = () => {
  hardeningRuleFormLoading.value = true

  setTimeout(() => {
    try {
      if (currentHardeningRule.value) {
        // 编辑模式
        const index = securityHardeningRuleList.value.findIndex(item => item.id === currentHardeningRule.value.id)
        if (index !== -1) {
          securityHardeningRuleList.value[index] = {
            ...hardeningRuleForm.value,
            id: currentHardeningRule.value.id,
            status: currentHardeningRule.value.status,
            createTime: currentHardeningRule.value.createTime
          }
        }
      } else {
        // 新增模式
        const newRule = {
          ...hardeningRuleForm.value,
          id: Date.now(),
          status: '启用',
          createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        }
        securityHardeningRuleList.value.push(newRule)
      }

      saveHardeningRulesToCache()
      showAddHardeningRuleDialog.value = false
      resetHardeningRuleForm()
      ElMessage.success(currentHardeningRule.value ? '编辑规则成功' : '添加规则成功')
    } catch (error) {
      console.error('保存安全加固规则失败:', error)
      ElMessage.error('保存失败')
    } finally {
      hardeningRuleFormLoading.value = false
    }
  }, 1000)
}

// 重置安全加固规则表单
const resetHardeningRuleForm = () => {
  currentHardeningRule.value = null
  hardeningRuleForm.value = {
    ruleName: '',
    ruleType: '数据加密',
    dataSetName: '',
    encryptionAlgorithm: 'AES-256',
    encryptionFields: '',
    accessControlLevel: '',
    contentFilterRules: ''
  }
}

// 日志管理相关函数
// 数据集日志管理按钮
const onClickLogManagement = () => {
  // loadLogsFromCache()
  showLogDialog.value = true
}

// 加载日志数据
const loadLogsFromCache = () => {
  try {
    const cached = localStorage.getItem(LOG_STORAGE_KEY)
    if (cached) {
      logList.value = JSON.parse(cached)
    } else {
      logList.value = generateMockLogs()
      saveLogsToCache()
    }
  } catch (error) {
    console.error('加载日志数据失败:', error)
    logList.value = generateMockLogs()
  }
}

// 保存日志到缓存
const saveLogsToCache = () => {
  try {
    localStorage.setItem(LOG_STORAGE_KEY, JSON.stringify(logList.value))
  } catch (error) {
    console.error('保存日志数据失败:', error)
  }
}

// 生成模拟日志数据
const generateMockLogs = (): DataSetLog[] => {
  // 基于实际数据集列表生成日志数据
  const actualDataSets = dataSetList.value.length > 0
    ? dataSetList.value.map(ds => ds.name)
    : ['用户信息数据集', '订单数据集', '产品数据集']

  const operations = ['查看', '新增', '编辑', '删除']
  const users = ['张三', '李四', '王五', '赵六']

  return Array.from({ length: 20 }, (_, index) => ({
    id: (index + 1).toString(),
    sequence: index + 1,
    operatorUser: users[Math.floor(Math.random() * users.length)],
    operationType: operations[Math.floor(Math.random() * operations.length)],
    operationDataSource: actualDataSets[Math.floor(Math.random() * actualDataSets.length)],
    operationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      .toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
      }).replace(/\//g, '.')
  }))
}

// 添加操作日志
const addOperationLog = (operationType: string, dataSetName: string) => {
  const newLog: DataSetLog = {
    id: Date.now().toString(),
    sequence: logList.value.length + 1,
    operatorUser: '当前用户',
    operationType,
    operationDataSource: dataSetName,
    operationTime: new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    }).replace(/\//g, '.') + ' ' + new Date().toLocaleTimeString('zh-CN')
  }

  logList.value.unshift(newLog) // 新日志添加到顶部
  saveLogsToCache()
}

// 获取过滤后的日志数据
const getFilteredLogs = () => {
  let filtered = logList.value

  if (logSearchForm.value.dataSource) {
    filtered = filtered.filter(item =>
      item.operationDataSource.toLowerCase().includes(logSearchForm.value.dataSource.toLowerCase())
    )
  }
  if (logSearchForm.value.operationTime) {
    filtered = filtered.filter(item =>
      item.operationTime.includes(logSearchForm.value.operationTime)
    )
  }

  return filtered
}

// 日志搜索
const onLogSearch = () => {
  // 搜索逻辑已在 getFilteredLogs 中实现
}

// 日志重置
const onLogReset = () => {
  logSearchForm.value = { dataSource: '', operationTime: '' }
}

// 数据集审计下拉菜单
const handleDataAuditCommand = (command: string) => {
  switch (command) {
    case 'permissionAudit':
      ElMessage.info('数据集权限审计功能开发中...')
      break
    case 'accessAudit':
      onClickAccessAudit()
      break
  }
}

// 更多操作下拉菜单
const handleMoreCommand = (command: string) => {
  switch (command) {
    case 'accessPermissionManagement':
      onClickAccessPermission()
      break
    case 'exceptionHandling':
      onClickExceptionHandling()
      break
    case 'qualityReportManagement':
      onClickBackupRule()
      break
    case 'dataSetIdentification':
      onClickComplianceCheck()
      break
    case 'integrationStrategy':
      onClickDataValidation()
      break
    case 'transformationRule':
      onClickBackupVerify()
      break
    case 'formatFileConfig':
      onClickSecurityVerification()
      break
    case 'dataSetAccessRule':
      onClickSecurityHardening()
      break
    case 'accessAnalysisMonitor':
      onClickLogManagement()
      break
    case 'dataSetHealthMonitor':
      ElMessage.info('数据集健康监控功能开发中...')
      break
    case 'sourceAnalysis':
      onClickPerformanceMonitor()
      break
    case 'archiveManagement':
      onClickArchiveManagement()
      break
    case 'permissionAudit':
      onClickPermissionAudit()
      break
    case 'accessAudit':
      onClickAccessAudit()
      break
    case 'logAnalysis':
      onClickLogAnalysis()
      break
  }
}

// 访问权限管理相关函数
// 加载权限数据
const loadAccessPermissionData = () => {
  try {
    const savedData = localStorage.getItem('dataSetPermissionAuditData')
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }

    // 加载当前数据集的权限设置（暂时使用第一个数据集作为示例）
    const currentDataSetName = dataSetList.value[0]?.name
    if (currentDataSetName) {
      const existingPermission = permissionAuditData.value.find(
        item => item.dataSetName === currentDataSetName
      )

      if (existingPermission) {
        // 回显已保存的权限设置
        accessPermissionForm.value = {
          dataSet: existingPermission.dataSetName,
          objectType: existingPermission.objectType,
          objectSelection: existingPermission.objectName,
          readPermission: existingPermission.readPermission,
          tablePermission: existingPermission.tablePermission,
          fieldPermission: existingPermission.fieldPermission,
          viewPermission: existingPermission.viewPermission,
          editPermission: existingPermission.editPermission,
          addPermission: existingPermission.addPermission,
          deletePermission: existingPermission.deletePermission
        }
        return
      }
    }

    // 如果没有找到已保存的数据，使用默认值
    accessPermissionForm.value = {
      dataSet: '',
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  } catch (error) {
    console.error('加载访问权限数据失败:', error)
    // 出错时使用默认值
    accessPermissionForm.value = {
      dataSet: '',
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  }
}

// 打开异常处理弹窗
const onClickExceptionHandling = () => {
  showExceptionHandlingDialog.value = true
}

// 处理异常
const handleException = (row: any) => {
  currentException.value = row
  exceptionDetailMode.value = 'edit'
  processingDescription.value = ''
  showExceptionDetailDialog.value = true
}

// 查看异常
const viewException = (row: any) => {
  currentException.value = row
  exceptionDetailMode.value = 'view'
  showExceptionDetailDialog.value = true
}

// 保存异常处理
const saveExceptionProcessing = () => {
  if (!processingDescription.value.trim()) {
    ElMessage.warning('请输入处理描述')
    return
  }

  // 更新异常状态和处理描述
  const exception = exceptionList.value.find(item => item.id === currentException.value.id)
  if (exception) {
    exception.status = '已处理'
    exception.processingDescription = processingDescription.value
  }

  ElMessage.success('处理成功')
  showExceptionDetailDialog.value = false
}

// 打开性能管理与监控弹窗
const onClickPerformanceMonitor = () => {
  showPerformanceMonitorDialog.value = true
  // 延迟初始化图表，确保DOM已渲染
  nextTick(() => {
    initPerformanceChart()
  })
}

// 切换图表类型
const switchChartType = (type: string) => {
  activeChartType.value = type
  updatePerformanceChart()
}

// 初始化性能图表
let performanceChart: echarts.ECharts | null = null
const initPerformanceChart = () => {
  if (!performanceChartRef.value) return

  performanceChart = echarts.init(performanceChartRef.value)
  updatePerformanceChart()
}

// 更新性能图表
const updatePerformanceChart = () => {
  if (!performanceChart) return

  const data = getCurrentChartData.value
  const option = {
    title: {
      text: getChartTitle(),
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>${point.seriesName}: ${point.value}${getChartUnit()}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map(item => item.time),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#666',
        formatter: (value: number) => `${value}${getChartUnit()}`
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa'
        }
      }
    },
    series: [
      {
        name: getChartTitle(),
        type: 'line',
        smooth: true,
        data: data.map(item => item.value),
        lineStyle: {
          color: '#409eff',
          width: 2
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ]
          }
        }
      }
    ]
  }

  performanceChart.setOption(option)
}

// 获取图表标题
const getChartTitle = () => {
  switch (activeChartType.value) {
    case 'throughput':
      return '数据吞吐量'
    case 'response':
      return '查询响应时间'
    case 'connections':
      return '连接数'
    default:
      return '性能趋势'
  }
}

// 监听图表类型变化
watch(activeChartType, () => {
  updatePerformanceChart()
})

// 获取当前图表数据
const getCurrentChartData = computed(() => {
  const key = activeChartType.value as keyof typeof performanceChartData.value
  return performanceChartData.value[key] || []
})

// 获取图表最大值
const getChartMaxValue = () => {
  const data = getCurrentChartData.value
  if (data.length === 0) return 100
  return Math.max(...data.map(item => item.value)) * 1.2
}

// 获取图表点坐标
const getChartPoints = () => {
  const data = getCurrentChartData.value
  if (data.length === 0) return ''

  const maxValue = getChartMaxValue()
  return data.map((point, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = 100 - (point.value / maxValue) * 100
    return `${x},${y}`
  }).join(' ')
}

// 获取当前值
const getCurrentValue = () => {
  const data = getCurrentChartData.value
  if (data.length === 0) return 0
  return data[data.length - 1].value
}

// 获取图表单位
const getChartUnit = () => {
  switch (activeChartType.value) {
    case 'throughput':
      return 'MB/s'
    case 'response':
      return 'ms'
    case 'connections':
      return ''
    default:
      return ''
  }
}

// 打开数据归档管理弹窗
const onClickArchiveManagement = () => {
  showArchiveManagementDialog.value = true
}

// 计算归档统计数据
const archiveStats = computed(() => {
  const count = archiveList.value.length
  const totalSize = archiveList.value.reduce((sum, item) => {
    const size = parseFloat(item.size.replace(' TB', ''))
    return sum + size
  }, 0)

  // 最近归档时间只显示年月日
  let latestTime = '无'
  if (archiveList.value.length > 0) {
    const timeStr = archiveList.value[0].archiveTime
    // 提取年月日部分，格式：2025.7.9
    latestTime = timeStr.split(' ')[0] || timeStr
  }

  return {
    count,
    totalSize: `${totalSize.toFixed(1)}TB`,
    latestTime
  }
})

// 查看归档详情
const viewArchiveDetail = (row: any) => {
  ElMessage.info(`查看归档详情：${row.dataSetName}`)
  // 这里可以添加查看归档详情的逻辑
}

// 添加归档条件
const addArchiveCondition = () => {
  archiveRuleForm.value.archiveConditions.push({
    field: '数据创建时间',
    operator: '大于',
    value: '',
    unit: '天'
  })
}

// 删除归档条件
const removeArchiveCondition = (index: number) => {
  archiveRuleForm.value.archiveConditions.splice(index, 1)
}

// 切换归档规则状态
const toggleArchiveRuleStatus = (row: any) => {
  row.status = row.status === '启用' ? '禁用' : '启用'
}

// 编辑归档规则
const editArchiveRule = (row: any) => {
  // 填充表单数据
  archiveRuleForm.value = {
    id: row.id,
    ruleName: row.ruleName,
    ruleDescription: row.ruleDescription || '',
    applicableDataSets: row.applicableDataSets.split('，'),
    archiveConditions: [
      {
        field: '数据创建时间',
        operator: '大于',
        value: '30',
        unit: '天'
      }
    ],
    executionPeriod: row.executionPeriod,
    executionTime: row.executionTime || '',
    archiveLocation: row.archiveLocation
  }
  showAddArchiveRuleDialog.value = true
}

// 删除归档规则
const deleteArchiveRule = (row: any) => {
  const index = archiveRuleList.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    archiveRuleList.value.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

// 保存归档规则
const saveArchiveRule = () => {
  // 验证表单
  if (!archiveRuleForm.value.ruleName.trim()) {
    ElMessage.warning('请输入规则名称')
    return
  }

  if (archiveRuleForm.value.applicableDataSets.length === 0) {
    ElMessage.warning('请选择适用数据集')
    return
  }

  if (!archiveRuleForm.value.executionPeriod) {
    ElMessage.warning('请选择执行周期')
    return
  }

  if (!archiveRuleForm.value.archiveLocation) {
    ElMessage.warning('请选择归档存储位置')
    return
  }

  // 构建归档条件描述
  const conditionDesc = archiveRuleForm.value.archiveConditions.map(condition =>
    `${condition.field} ${condition.operator} ${condition.value}${condition.unit}`
  ).join('，')

  const ruleData = {
    id: archiveRuleForm.value.id || Date.now(),
    ruleName: archiveRuleForm.value.ruleName,
    ruleDescription: archiveRuleForm.value.ruleDescription,
    applicableDataSets: archiveRuleForm.value.applicableDataSets.join('，'),
    archiveCondition: conditionDesc,
    executionPeriod: archiveRuleForm.value.executionPeriod,
    executionTime: archiveRuleForm.value.executionTime,
    archiveLocation: archiveRuleForm.value.archiveLocation,
    status: '启用'
  }

  if (archiveRuleForm.value.id) {
    // 编辑模式
    const index = archiveRuleList.value.findIndex(item => item.id === archiveRuleForm.value.id)
    if (index > -1) {
      archiveRuleList.value[index] = ruleData
    }
  } else {
    // 新增模式
    archiveRuleList.value.push(ruleData)
  }

  ElMessage.success('归档规则保存成功')
  showAddArchiveRuleDialog.value = false

  // 重置表单
  resetArchiveRuleForm()
}

// 重置归档规则表单
const resetArchiveRuleForm = () => {
  archiveRuleForm.value = {
    id: null,
    ruleName: '',
    ruleDescription: '',
    applicableDataSets: [],
    archiveConditions: [
      {
        field: '数据创建时间',
        operator: '大于',
        value: '30',
        unit: '天'
      }
    ],
    executionPeriod: '',
    executionTime: '',
    archiveLocation: ''
  }
}

// 打开权限审计弹窗
const onClickPermissionAudit = () => {
  // 从localStorage加载数据集权限审计数据
  try {
    const savedData = localStorage.getItem('dataSetPermissionAuditData')
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }
  } catch (error) {
    console.error('加载数据集权限审计数据失败:', error)
  }
  showPermissionAuditDialog.value = true
}

// 数据集访问审计
const onClickAccessAudit = () => {
  // 确保日志数据与数据集管理中的数据一致
  regenerateLogsBasedOnDataSets()
  // 扩展现有日志数据，添加访问审计字段
  extendLogDataForAccessAudit()
  showAccessAuditDialog.value = true
}

// 基于数据集重新生成日志数据
const regenerateLogsBasedOnDataSets = () => {
  // 确保每个数据集都有对应的操作日志
  dataSetList.value.forEach((dataSet, index) => {
    const existingLog = logList.value.find(log => log.operationDataSource === dataSet.name)
    if (!existingLog) {
      // 为没有日志的数据集创建日志
      const newLog: DataSetLog = {
        id: `${Date.now()}_${index}`,
        sequence: logList.value.length + 1,
        operatorUser: '系统管理员',
        operationType: '创建',
        operationDataSource: dataSet.name,
        operationTime: dataSet.createTime || new Date().toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric'
        }).replace(/\//g, '.')
      }
      logList.value.push(newLog)
    }
  })
}

// 扩展日志数据为访问审计数据
const extendLogDataForAccessAudit = () => {
  logList.value.forEach(log => {
    if (!log.accessTime) {
      // 生成随机访问时间（在操作时间基础上随机偏移）
      const baseTime = new Date(log.operationTime)
      const randomOffset = Math.floor(Math.random() * 3600000) // 随机1小时内
      log.accessTime = new Date(baseTime.getTime() + randomOffset).toLocaleString('zh-CN')

      // 生成访问对象（功能对象/接口对象/数据库对象）
      const accessObjects = [
        '数据查询功能', '数据导出接口', '用户表', '订单表',
        '报表生成功能', 'API接口', '日志表', '配置表',
        '数据分析功能', '统计分析接口', '权限表', '系统表'
      ]
      log.accessUser = accessObjects[Math.floor(Math.random() * accessObjects.length)]

      // 生成随机IP地址
      const ipBase = '192.168.1.'
      const ipSuffix = Math.floor(Math.random() * 254) + 1
      log.ipAddress = ipBase + ipSuffix
    }
  })
}

// 数据集访问审计搜索
const onAccessAuditSearch = () => {
  // 基于搜索条件过滤数据
}

// 过滤后的访问审计数据
const filteredAccessAuditData = computed(() => {
  let filtered = [...logList.value]

  if (accessAuditSearchForm.value.dataSet) {
    filtered = filtered.filter(item =>
      item.operationDataSource === accessAuditSearchForm.value.dataSet
    )
  }

  if (accessAuditSearchForm.value.operationType) {
    filtered = filtered.filter(item =>
      item.operationType === accessAuditSearchForm.value.operationType
    )
  }

  if (accessAuditSearchForm.value.operatorUser) {
    filtered = filtered.filter(item =>
      item.operatorUser.includes(accessAuditSearchForm.value.operatorUser)
    )
  }

  if (accessAuditSearchForm.value.startTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) >= new Date(accessAuditSearchForm.value.startTime)
    )
  }

  if (accessAuditSearchForm.value.endTime) {
    filtered = filtered.filter(item =>
      new Date(item.operationTime) <= new Date(accessAuditSearchForm.value.endTime)
    )
  }

  return filtered
})

// 日志分析与行为监控
const onClickLogAnalysis = () => {
  showLogAnalysisDialog.value = true
  // 延迟初始化图表，确保DOM已渲染
  nextTick(() => {
    initLogAnalysisCharts()
  })
}

// 初始化日志分析图表
let logAnalysisChart: echarts.ECharts | null = null
let logDistributionChart: echarts.ECharts | null = null

const initLogAnalysisCharts = () => {
  if (logAnalysisChartRef.value && logDistributionChartRef.value) {
    initVisitStatsChart()
    initVisitTypeChart()
  }
}

// 初始化访问次数统计图表
const initVisitStatsChart = () => {
  if (!logAnalysisChartRef.value) return

  logAnalysisChart = echarts.init(logAnalysisChartRef.value)
  const option = {
    title: {
      text: '访问次数',
      left: 'left',
      textStyle: {
        fontSize: 14,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const point = params[0]
        return `${point.name}<br/>访问次数: ${point.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: visitStatsData.value.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa'
        }
      }
    },
    series: [
      {
        data: visitStatsData.value.map(item => item.count),
        type: 'bar',
        itemStyle: {
          color: '#409eff'
        },
        barWidth: '60%'
      }
    ]
  }

  logAnalysisChart.setOption(option)
}

// 初始化访问类型分布图表
const initVisitTypeChart = () => {
  if (!logDistributionChartRef.value) return

  logDistributionChart = echarts.init(logDistributionChartRef.value)
  const option = {
    title: {
      text: '访问类型分布',
      left: 'left',
      textStyle: {
        fontSize: 14,
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: visitTypeData.value.map(item => item.name)
    },
    series: [
      {
        name: '访问类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: visitTypeData.value.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: { color: item.color }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  logDistributionChart.setOption(option)
}

// 日志分析分页处理
const handleLogAnalysisSizeChange = (val: number) => {
  logAnalysisPagination.pageSize = val
  logAnalysisPagination.currentPage = 1
}

const handleLogAnalysisCurrentChange = (val: number) => {
  logAnalysisPagination.currentPage = val
}

// 打开访问权限调整弹窗
const onClickAccessPermission = () => {
  loadAccessPermissionData()
  showAccessPermissionDialog.value = true
}

// 根据对象类型获取对象选择数据
const getObjectSelectionData = computed(() => {
  return accessPermissionForm.value.objectType === '用户' ? userData.value : roleData.value
})

// 保存访问权限设置
const saveAccessPermission = () => {
  // 验证必填字段
  if (!accessPermissionForm.value.dataSet) {
    ElMessage.warning('请选择数据集')
    return
  }

  if (!accessPermissionForm.value.objectSelection) {
    ElMessage.warning('请选择用户或角色')
    return
  }

  const currentDataSetName = accessPermissionForm.value.dataSet

  // 检查是否已存在该数据集的权限设置
  const existingIndex = permissionAuditData.value.findIndex(
    item => item.dataSetName === currentDataSetName && item.objectName === accessPermissionForm.value.objectSelection
  )

  const permissionData = {
    id: existingIndex >= 0 ? permissionAuditData.value[existingIndex].id : Date.now(),
    dataSetName: currentDataSetName,
    objectType: accessPermissionForm.value.objectType,
    objectName: accessPermissionForm.value.objectSelection,
    readPermission: accessPermissionForm.value.readPermission,
    tablePermission: accessPermissionForm.value.tablePermission,
    fieldPermission: accessPermissionForm.value.fieldPermission,
    viewPermission: accessPermissionForm.value.viewPermission,
    editPermission: accessPermissionForm.value.editPermission,
    addPermission: accessPermissionForm.value.addPermission,
    deletePermission: accessPermissionForm.value.deletePermission,
    createTime: existingIndex >= 0 ? permissionAuditData.value[existingIndex].createTime : new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN')
  }

  if (existingIndex >= 0) {
    // 更新已存在的权限设置
    permissionAuditData.value[existingIndex] = permissionData
  } else {
    // 添加新的权限设置
    permissionAuditData.value.push(permissionData)
  }

  // 保存到localStorage
  localStorage.setItem('dataSetPermissionAuditData', JSON.stringify(permissionAuditData.value))

  ElMessage.success('访问权限调整保存成功')
  showAccessPermissionDialog.value = false
}

// 表格按钮点击事件
const onTableButtonClick = (data: any) => {
  console.log('onTableButtonClick data:', data)

  let btn, row

  // 处理不同的调用格式
  if (data && typeof data === 'object' && data.btn && data.scope) {
    // 格式1: { btn, scope } - 来自普通按钮点击
    btn = data.btn
    row = data.scope
  } else if (data && data.code) {
    // 格式2: 直接传递按钮对象 - 来自ElPopconfirm的@confirm事件
    // 这种情况下，第二个参数是行数据，但这里只有一个参数
    // 我们需要从当前作用域获取行数据
    btn = data
    row = arguments[1] // 第二个参数是行数据
  } else {
    console.error('无法解析按钮点击数据:', data, arguments)
    return
  }

  console.log('解析后的 btn:', btn, 'row:', row)

  if (!btn || !row) {
    console.error('按钮或行数据缺失:', { btn, row })
    return
  }

  currentRow.value = row

  switch (btn.code) {
    case 'view':
      dialogMode.value = 'view'
      dialogForm.value = { ...row }
      showDialogForm.value = true

      // 添加操作日志
      addOperationLog('查看', row.name)

      break
    case 'edit':
      dialogMode.value = 'edit'
      dialogForm.value = { ...row }
      showDialogForm.value = true
      break
    case 'delete':
      // 对于删除按钮，ElPopconfirm已经处理了确认逻辑
      // 直接执行删除操作
      const index = dataSetList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        const deletedDataSet = dataSetList.value[index]
        dataSetList.value.splice(index, 1)

        // 添加操作日志
        addOperationLog('删除', deletedDataSet.name)

        saveDataToCache()
        ElMessage.success('删除成功')
        console.log('删除成功，剩余数据条数:', dataSetList.value.length)
      }
      break
    case 'dataValidation':
      showDataValidationDialog.value = true
      break
    case 'backupVerify':
      showBackupVerifyDialog.value = true
      break
    case 'complianceCheck':
      showComplianceCheckDialog.value = true
      break
    case 'securityVerification':
      showSecurityVerificationDialog.value = true
      break
    case 'securityHardening':
      showSecurityHardeningDialog.value = true
      break
    case 'logManagement':
      showLogDialog.value = true
      break
    case 'exceptionHandling':
      showExceptionHandlingDialog.value = true
      break
    case 'performanceMonitor':
      showPerformanceMonitorDialog.value = true
      break
    case 'archiveManagement':
      showArchiveManagementDialog.value = true
      break
    case 'permissionAudit':
      showPermissionAuditDialog.value = true
      break
    case 'accessAudit':
      showAccessAuditDialog.value = true
      break
    case 'logAnalysis':
      showLogAnalysisDialog.value = true
      break
  }
}

// 弹窗确认
const onDialogConfirm = () => {
  if (dialogMode.value === 'view') {
    showDialogForm.value = false
    return
  }

  // 表单验证逻辑
  loading.value = true

  setTimeout(() => {
    if (dialogMode.value === 'add') {
      const newDataSet: DataSet = {
        ...dialogForm.value as DataSet,
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        createUser: '当前用户',
        dataSize: '0MB',
        recordCount: 0,
        lastUpdateTime: new Date().toLocaleString(),
        dataSourceName: '新建数据源',
        dataSourceType: 'MySql'
      }
      dataSetList.value.unshift(newDataSet)

      // 添加操作日志
      addOperationLog('创建', newDataSet.name)

      ElMessage.success('新增成功')
    } else if (dialogMode.value === 'edit') {
      const index = dataSetList.value.findIndex(item => item.id === currentRow.value?.id)
      if (index !== -1) {
        const oldDataSet = dataSetList.value[index]
        dataSetList.value[index] = {
          ...dataSetList.value[index],
          ...dialogForm.value,
          lastUpdateTime: new Date().toLocaleString()
        }

        // 添加操作日志
        addOperationLog('编辑', oldDataSet.name)

        ElMessage.success('编辑成功')
      }
    }

    saveDataToCache()
    showDialogForm.value = false
    loading.value = false
  }, 1000)
}

// 组件挂载 - 数据已在dataSetList中初始化，不需要额外加载
onMounted(() => {
  // 加载权限审计数据
  try {
    const savedData = localStorage.getItem('dataSetPermissionAuditData')
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }
  } catch (error) {
    console.error('加载权限审计数据失败:', error)
  }

  // 数据已在页面初始化时设置，无需从缓存加载
  console.log('数据集页面已挂载，数据条数:', dataSetList.value.length)
})
</script>

<style scoped>
.data-set-tab {
  height: 100%;
}

.top-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-fields {
  flex: 1;
}

.search-buttons {
  display: flex;
  gap: 8px;
  margin-left: 20px;
}

/* 批量导入弹窗样式 */
.import-content {
  padding: 20px;
}

.import-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.import-steps {
  margin-bottom: 20px;
}

.step-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.steps {
  display: flex;
  align-items: center;
  gap: 10px;
}

.step {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

.step-text {
  font-size: 14px;
  color: #333;
}

.step-divider {
  width: 30px;
  height: 1px;
  background: #ddd;
}

.template-download {
  margin-bottom: 20px;
}

.upload-area {
  margin-top: 20px;
}

.selected-file {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  font-size: 14px;
  color: #333;
}

/* 备份规则弹窗样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.form-section {
  margin-bottom: 24px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style>
