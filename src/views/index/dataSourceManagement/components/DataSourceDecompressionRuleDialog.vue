<!-- 数据源解压规则配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源解压规则配置"
    width="700px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="decompression-rule-content" style="padding: 20px; min-height: 400px;">
      <!-- 数据源压缩规则 -->
      <div class="form-section">
        <h4 style="margin-bottom: 15px; color: #333;">数据源压缩规则</h4>

        <div class="checkbox-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
          <el-checkbox v-model="form.compressionRules.zipFormat">压缩格式配置</el-checkbox>
          <el-checkbox v-model="form.compressionRules.rarFormat">历史数据提取</el-checkbox>
          <el-checkbox v-model="form.compressionRules.sevenZipFormat">解析文件</el-checkbox>
          <el-checkbox v-model="form.compressionRules.tarFormat">压缩格式</el-checkbox>
          <el-checkbox v-model="form.compressionRules.gzFormat">ZIP</el-checkbox>
          <el-checkbox v-model="form.compressionRules.bz2Format">7Z</el-checkbox>
          <el-checkbox v-model="form.compressionRules.arFormat">AR</el-checkbox>
        </div>

        <div class="form-item" style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 8px;">文件来源配置</label>
          <div class="checkbox-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
            <el-checkbox v-model="form.compressionRules.fileSourceConfig">仅支持txt/log</el-checkbox>
            <el-checkbox v-model="form.compressionRules.analysisConfig">所有存储格式化文件</el-checkbox>
            <el-checkbox v-model="form.compressionRules.autoConfig">自动配置</el-checkbox>
            <el-checkbox v-model="form.compressionRules.backupConfig">备份文件</el-checkbox>
            <el-checkbox v-model="form.compressionRules.moveConfig">移动配置到目录</el-checkbox>
          </div>
        </div>
      </div>

      <!-- 数据源解压规则 -->
      <div class="form-section" style="margin-top: 30px;">
        <h4 style="margin-bottom: 15px; color: #333;">数据源解压规则</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">解压缩条件</label>
          <div class="checkbox-row" style="display: flex; gap: 20px;">
            <el-checkbox v-model="form.decompressionRules.manualDecompression">手动触发</el-checkbox>

            <el-checkbox v-model="form.decompressionRules.autoDelete">用户请求查询时自动触发</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">解压路径</label>
          <div style="display: flex; gap: 10px;">
            <el-input
              v-model="form.decompressionRules.path"
              placeholder="请选择"
              readonly
              style="flex: 1;"
            />
            <el-button type="primary" @click="selectPath">选择路径</el-button>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  compressionRules: {
    zipFormat: false,
    rarFormat: false,
    sevenZipFormat: false,
    tarFormat: false,
    gzFormat: false,
    bz2Format: false,
    arFormat: false,
    fileSourceConfig: false,
    analysisConfig: false,
    autoConfig: false,
    backupConfig: false,
    moveConfig: false
  },
  decompressionRules: {
    autoDecompression: false,
    manualDecompression: false,
    userRequest: false,
    autoDelete: false,
    path: ''
  }
})

// 缓存键
const STORAGE_KEY = 'dataSourceDecompressionRule'

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        compressionRules: {
          zipFormat: config.compressionRules?.zipFormat || false,
          rarFormat: config.compressionRules?.rarFormat || false,
          sevenZipFormat: config.compressionRules?.sevenZipFormat || false,
          tarFormat: config.compressionRules?.tarFormat || false,
          gzFormat: config.compressionRules?.gzFormat || false,
          bz2Format: config.compressionRules?.bz2Format || false,
          arFormat: config.compressionRules?.arFormat || false,
          fileSourceConfig: config.compressionRules?.fileSourceConfig || false,
          analysisConfig: config.compressionRules?.analysisConfig || false,
          autoConfig: config.compressionRules?.autoConfig || false,
          backupConfig: config.compressionRules?.backupConfig || false,
          moveConfig: config.compressionRules?.moveConfig || false
        },
        decompressionRules: {
          autoDecompression: config.decompressionRules?.autoDecompression || false,
          manualDecompression: config.decompressionRules?.manualDecompression || false,
          userRequest: config.decompressionRules?.userRequest || false,
          autoDelete: config.decompressionRules?.autoDelete || false,
          path: config.decompressionRules?.path || ''
        }
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据源解压规则配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据源解压规则配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    compressionRules: {
      zipFormat: false,
      rarFormat: false,
      sevenZipFormat: false,
      tarFormat: false,
      gzFormat: false,
      bz2Format: false,
      arFormat: false,
      fileSourceConfig: false,
      analysisConfig: false,
      autoConfig: false,
      backupConfig: false,
      moveConfig: false
    },
    decompressionRules: {
      autoDecompression: false,
      manualDecompression: false,
      userRequest: false,
      autoDelete: false,
      path: ''
    }
  }
}

// 选择路径
const selectPath = () => {
  // 模拟文件路径选择
  const input = document.createElement('input')
  input.type = 'file'
  input.webkitdirectory = true
  input.onchange = (e: any) => {
    const files = e.target.files
    if (files && files.length > 0) {
      // 获取目录路径
      const path = files[0].webkitRelativePath.split('/')[0]
      form.value.decompressionRules.path = path
    }
  }
  input.click()
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据源解压规则配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.decompression-rule-content {
  .form-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }
  
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
  
  .checkbox-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    
    .el-checkbox {
      margin-right: 0;
      white-space: nowrap;
    }
  }
  
  .radio-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .el-radio {
      margin-right: 0;
    }
  }
}
</style>
