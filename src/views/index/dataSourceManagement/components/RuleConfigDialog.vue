<template>
  <Dialog
    v-model="visible"
    title="数据源校验"
    width="700px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div class="rule-config" style="padding: 20px; min-height: 500px;">
      <!-- 数据源校验 -->
      <div class="section">
        <div class="section-title">数据源校验</div>
        <div class="form-section">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">校验维度</label>
            <div class="checkbox-group" style="display: flex; gap: 20px;">
              <el-checkbox v-model="validationForm.completeness">完整性（非空校验）</el-checkbox>
              <el-checkbox v-model="validationForm.accuracy">准确性（格式校验）</el-checkbox>
              <el-checkbox v-model="validationForm.uniqueness">唯一性（数据源名称重复校验）</el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据脱敏 -->
      <div class="section" style="margin-top: 30px;">
        <div class="section-title">数据脱敏</div>
        <div class="form-section">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">
              <span style="color: red;">*</span> 脱敏方法：
            </label>
            <el-select v-model="validationForm.desensitizationMethod" placeholder="请选择" style="width: 100%;">
              <el-option label="部分遮蔽" value="partial_mask" />
              <el-option label="哈希加密" value="hash_encrypt" />
              <el-option label="数据替换" value="data_replace" />
              <el-option label="置空" value="nullify" />
            </el-select>
          </div>

          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">
              <span style="color: red;">*</span> 是否启动：
            </label>
            <el-switch v-model="validationForm.desensitizationEnabled" />
          </div>
        </div>
      </div>

      <!-- 数据更新频率设置 -->
      <div class="section" style="margin-top: 30px;">
        <div class="section-title">数据更新频率设置</div>
        <div class="form-section">
          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">执行频率</label>
            <div style="display: flex; align-items: center; gap: 15px;">
              <el-select v-model="validationForm.frequency" placeholder="请选择" style="width: 150px;">
                <el-option label="每日" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>

              <span>时分秒</span>
              <el-time-picker
                v-model="validationForm.executionTime"
                format="HH:mm:ss"
                placeholder="选择时间"
                style="width: 150px;"
              />
            </div>
          </div>

          <div class="form-item" style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 8px;">
              <span style="color: red;">*</span> 是否启动：
            </label>
            <el-switch v-model="validationForm.frequencyEnabled" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 校验表单
const validationForm = ref({
  completeness: false,
  accuracy: false,
  uniqueness: false,
  desensitizationMethod: '',
  desensitizationEnabled: false,
  frequency: '',
  executionTime: null,
  frequencyEnabled: false
})

// 保存配置
const handleSave = () => {
  try {
    // 验证必填项
    if (!validationForm.value.desensitizationMethod) {
      ElMessage.error('请选择脱敏方法')
      return
    }

    // 保存到localStorage
    const config = {
      validation: validationForm.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('ruleConfig_data', JSON.stringify(config))

    ElMessage.success('数据源校验配置保存成功')
    visible.value = false
  } catch (error) {
    console.error('保存数据源校验配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 加载配置
const loadConfig = () => {
  try {
    const cached = localStorage.getItem('ruleConfig_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.validation) {
        validationForm.value = { ...validationForm.value, ...config.validation }
      }
    }
  } catch (error) {
    console.error('加载数据源校验配置失败:', error)
  }
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfig()
  }
})
</script>

<style scoped lang="scss">
.rule-config {
  .section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
    
    .form-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
