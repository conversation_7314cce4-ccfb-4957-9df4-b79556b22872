<!-- 集成策略与任务弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源集成策略与任务"
    width="1000px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="handleClose"
  >
    <div class="integration-strategy-content">
      <el-tabs v-model="activeTab">
        <!-- 数据源继承策略配置 Tab -->
        <el-tab-pane label="数据源继承策略配置" name="strategy">
          <div style="margin-bottom: 20px;">
            <el-button size="small" type="primary" @click="onClickAddStrategy">新增策略</el-button>
          </div>
          <TableV2
            :defaultTableData="strategyList"
            :columns="[
              { prop: 'sequence', label: '序号', width: 80 },
              { prop: 'strategyName', label: '策略名称' },
              { prop: 'sourceDataSource', label: '源数据源' },
              { prop: 'targetDataSource', label: '目标数据源' },
              { prop: 'executionTime', label: '执行时间' },
              { prop: 'status', label: '状态' },
              { prop: 'createTime', label: '创建时间' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="350"
            :loading="loading"
            :buttons="[
              { label: '查看', type: 'info', code: 'view' },
              { label: '编辑', type: 'primary', code: 'edit' },
              { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
            ]"
            @click-button="onStrategyAction"
          />
        </el-tab-pane>

        <!-- 数据源集成任务 Tab -->
        <el-tab-pane label="数据源集成任务" name="task">
          <div style="margin-bottom: 20px;">
            <el-button size="small" type="primary" @click="onClickAddTask">新增集成任务</el-button>
          </div>
          <TableV2
            :defaultTableData="taskList"
            :columns="[
              { prop: 'sequence', label: '序号', width: 80 },
              { prop: 'taskName', label: '任务名称' },
              { prop: 'relatedStrategy', label: '关联策略' },
              { prop: 'executionTime', label: '执行时间' },
              { prop: 'status', label: '状态' },
              { prop: 'createTime', label: '创建时间' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="350"
            :loading="loading"
            :buttons="[
              { label: '查看', type: 'info', code: 'view' },
              { label: '编辑', type: 'primary', code: 'edit' },
              { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
            ]"
            @click-button="onTaskAction"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增/编辑策略弹窗 -->
    <Dialog
      v-model="showStrategyFormDialog"
      :title="currentStrategy ? '编辑策略' : '新增策略'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetStrategyForm"
      @click-confirm="onConfirmStrategy"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          ref="strategyFormRef"
          v-model="strategyForm"
          :props="[
            { label: '策略名称', prop: 'strategyName', type: 'text', required: true, placeholder: '请输入' },
            { label: '源数据源', prop: 'sourceDataSource', type: 'select', required: true, options: getDataSourceOptions(), placeholder: '请选择' },
            { label: '策略描述', prop: 'description', type: 'textarea', placeholder: '请输入' },
            { label: '执行时间', prop: 'executionTime', type: 'select', required: true, options: executionTimeOptions, placeholder: '请选择' }
          ]"
          :rules="strategyFormRules"
          :enable-button="false"
        />

        <!-- 数据转换规则 -->
        <div style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 10px;">数据转换规则:</label>
          <el-checkbox-group v-model="strategyForm.transformationRules">
            <el-checkbox value="clean">数据清洗（去除重复、空值）</el-checkbox>
            <el-checkbox value="format">数据格式转换（日期格式化）</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 是否启用 -->
        <div style="margin-top: 20px; display: flex; align-items: center;">
          <label style="margin-right: 10px;">是否启用</label>
          <el-switch v-model="strategyForm.enabled" />
        </div>
      </div>
    </Dialog>

    <!-- 新增/编辑任务弹窗 -->
    <Dialog
      v-model="showTaskFormDialog"
      :title="currentTask ? '编辑任务' : '新增集成任务'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetTaskForm"
      @click-confirm="onConfirmTask"
    >
      <div style="padding: 20px; min-height: 300px;">
        <Form
          ref="taskFormRef"
          v-model="taskForm"
          :props="[
            { label: '任务名称', prop: 'taskName', type: 'text', required: true },
            { label: '关联策略', prop: 'relatedStrategy', type: 'select', required: true, options: getEnabledStrategyOptions() },
            { label: '任务描述', prop: 'description', type: 'textarea' },
            { label: '是否启用', prop: 'enabled', type: 'switch' }
          ]"
          :rules="taskFormRules"
          :enable-button="false"
        />
      </div>
    </Dialog>

    <!-- 查看详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      :title="detailTitle"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDetailDialog = false"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          v-if="currentDetail && activeTab === 'strategy'"
          v-model="currentDetail"
          :props="[
            { label: '策略名称', prop: 'strategyName', type: 'text', disabled: true },
            { label: '源数据源', prop: 'sourceDataSource', type: 'text', disabled: true },
            { label: '策略描述', prop: 'description', type: 'textarea', disabled: true },
            { label: '执行时间', prop: 'executionTime', type: 'text', disabled: true },
            { label: '继承规则', prop: 'inheritanceRulesText', type: 'text', disabled: true },
            { label: '状态', prop: 'status', type: 'text', disabled: true },
            { label: '创建时间', prop: 'createTime', type: 'text', disabled: true }
          ]"
          :enable-button="false"
        />
        <Form
          v-if="currentDetail && activeTab === 'task'"
          v-model="currentDetail"
          :props="[
            { label: '任务名称', prop: 'taskName', type: 'text', disabled: true },
            { label: '关联策略', prop: 'relatedStrategy', type: 'text', disabled: true },
            { label: '任务描述', prop: 'description', type: 'textarea', disabled: true },
            { label: '执行时间', prop: 'executionTime', type: 'text', disabled: true },
            { label: '状态', prop: 'status', type: 'text', disabled: true },
            { label: '创建时间', prop: 'createTime', type: 'text', disabled: true }
          ]"
          :enable-button="false"
        />
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
  dataSourceList: any[]
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('strategy')
const loading = ref(false)
const formLoading = ref(false)

// 策略相关
const strategyList = ref<any[]>([])
const showStrategyFormDialog = ref(false)
const strategyForm = ref<any>({})
const strategyFormRef = ref()
const currentStrategy = ref<any>(null)

// 任务相关
const taskList = ref<any[]>([])
const showTaskFormDialog = ref(false)
const taskForm = ref<any>({})
const taskFormRef = ref()
const currentTask = ref<any>(null)

// 详情相关
const showDetailDialog = ref(false)
const currentDetail = ref<any>(null)
const detailTitle = ref('')

// 缓存键
const STRATEGY_STORAGE_KEY = 'integrationStrategies'
const TASK_STORAGE_KEY = 'integrationTasks'

// 执行时间选项
const executionTimeOptions = [
  { label: '每天', value: '每天' },
  { label: '每周', value: '每周' },
  { label: '每月', value: '每月' }
]

// 继承规则选项
const inheritanceRuleOptions = [
  { label: '数据表结构', value: '数据表结构' },
  { label: '字段', value: '字段' },
  { label: '索引', value: '索引' },
  { label: '约束关系', value: '约束关系' }
]

// 表单验证规则
const strategyFormRules = {
  strategyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  sourceDataSource: [{ required: true, message: '请选择源数据源', trigger: 'change' }],
  executionTime: [{ required: true, message: '请输入执行时间', trigger: 'blur' }]
}

const taskFormRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  relatedStrategy: [{ required: true, message: '请选择关联策略', trigger: 'change' }]
}

// 获取数据源选项
const getDataSourceOptions = () => {
  return props.dataSourceList.map(ds => ({
    label: ds.name,
    value: ds.name
  }))
}

// 获取启用的策略选项
const getEnabledStrategyOptions = () => {
  return strategyList.value
    .filter(strategy => strategy.status === '启用')
    .map(strategy => ({
      label: strategy.strategyName,
      value: strategy.strategyName
    }))
}

// 加载策略数据
const loadStrategiesFromCache = () => {
  try {
    const cached = localStorage.getItem(STRATEGY_STORAGE_KEY)
    if (cached) {
      strategyList.value = JSON.parse(cached)
    } else {
      strategyList.value = []
    }
  } catch (error) {
    console.error('加载策略数据失败:', error)
    strategyList.value = []
  }
}

// 保存策略数据
const saveStrategiesToCache = () => {
  try {
    localStorage.setItem(STRATEGY_STORAGE_KEY, JSON.stringify(strategyList.value))
  } catch (error) {
    console.error('保存策略数据失败:', error)
  }
}

// 加载任务数据
const loadTasksFromCache = () => {
  try {
    const cached = localStorage.getItem(TASK_STORAGE_KEY)
    if (cached) {
      taskList.value = JSON.parse(cached)
    } else {
      taskList.value = []
    }
  } catch (error) {
    console.error('加载任务数据失败:', error)
    taskList.value = []
  }
}

// 保存任务数据
const saveTasksToCache = () => {
  try {
    localStorage.setItem(TASK_STORAGE_KEY, JSON.stringify(taskList.value))
  } catch (error) {
    console.error('保存任务数据失败:', error)
  }
}

// 新增策略
const onClickAddStrategy = () => {
  currentStrategy.value = null
  resetStrategyForm()
  showStrategyFormDialog.value = true
}

// 重置策略表单
const resetStrategyForm = () => {
  strategyForm.value = {
    strategyName: '',
    sourceDataSource: '',
    description: '',
    executionTime: '',
    transformationRules: [],
    enabled: true
  }
}

// 确认策略
const onConfirmStrategy = () => {
  strategyFormRef.value.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true
      setTimeout(() => {
        if (currentStrategy.value) {
          // 编辑
          const index = strategyList.value.findIndex(item => item.id === currentStrategy.value.id)
          if (index !== -1) {
            strategyList.value[index] = {
              ...currentStrategy.value,
              ...strategyForm.value,
              status: strategyForm.value.enabled ? '启用' : '禁用',
              updateTime: new Date().toLocaleString('zh-CN')
            }
          }
          ElMessage.success('编辑策略成功')
        } else {
          // 新增
          const newStrategy = {
            id: `strategy_${Date.now()}`,
            sequence: strategyList.value.length + 1,
            ...strategyForm.value,
            status: strategyForm.value.enabled ? '启用' : '禁用',
            createTime: new Date().toLocaleString('zh-CN')
          }
          strategyList.value.unshift(newStrategy)
          ElMessage.success('新增策略成功')
        }
        saveStrategiesToCache()
        showStrategyFormDialog.value = false
        formLoading.value = false
      }, 1000)
    }
  })
}

// 策略操作
const onStrategyAction = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      currentDetail.value = {
        ...row,
        inheritanceRulesText: Array.isArray(row.inheritanceRules) ? row.inheritanceRules.join('、') : row.inheritanceRules || '无'
      }
      detailTitle.value = '策略详情'
      showDetailDialog.value = true
      break
    case 'edit':
      currentStrategy.value = row
      strategyForm.value = { ...row, enabled: row.status === '启用' }
      showStrategyFormDialog.value = true
      break
    case 'delete':
      const strategyIndex = strategyList.value.findIndex(item => item.id === row.id)
      if (strategyIndex !== -1) {
        strategyList.value.splice(strategyIndex, 1)
        saveStrategiesToCache()
        ElMessage.success('删除策略成功')
      }
      break
  }
}

// 新增任务
const onClickAddTask = () => {
  currentTask.value = null
  resetTaskForm()
  showTaskFormDialog.value = true
}

// 重置任务表单
const resetTaskForm = () => {
  taskForm.value = {
    taskName: '',
    relatedStrategy: '',
    description: '',
    enabled: true
  }
}

// 确认任务
const onConfirmTask = () => {
  taskFormRef.value.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true
      setTimeout(() => {
        if (currentTask.value) {
          // 编辑
          const index = taskList.value.findIndex(item => item.id === currentTask.value.id)
          if (index !== -1) {
            taskList.value[index] = {
              ...currentTask.value,
              ...taskForm.value,
              status: taskForm.value.enabled ? '启用' : '禁用',
              updateTime: new Date().toLocaleString('zh-CN')
            }
          }
          ElMessage.success('编辑任务成功')
        } else {
          // 新增
          const newTask = {
            id: `task_${Date.now()}`,
            sequence: taskList.value.length + 1,
            ...taskForm.value,
            status: taskForm.value.enabled ? '启用' : '禁用',
            executionTime: '每日 10:30',
            createTime: new Date().toLocaleString('zh-CN')
          }
          taskList.value.unshift(newTask)
          ElMessage.success('新增任务成功')
        }
        saveTasksToCache()
        showTaskFormDialog.value = false
        formLoading.value = false
      }, 1000)
    }
  })
}

// 任务操作
const onTaskAction = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      currentDetail.value = row
      detailTitle.value = '任务详情'
      showDetailDialog.value = true
      break
    case 'edit':
      currentTask.value = row
      taskForm.value = { ...row, enabled: row.status === '启用' }
      showTaskFormDialog.value = true
      break
    case 'delete':
      const taskIndex = taskList.value.findIndex(item => item.id === row.id)
      if (taskIndex !== -1) {
        taskList.value.splice(taskIndex, 1)
        saveTasksToCache()
        ElMessage.success('删除任务成功')
      }
      break
  }
}



// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 初始化数据
const initData = () => {
  loadStrategiesFromCache()
  loadTasksFromCache()
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initData()
  }
})
</script>

<style lang="scss" scoped>
.integration-strategy-content {
  // 样式可以在这里添加
}
</style>
