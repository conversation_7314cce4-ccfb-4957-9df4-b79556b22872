<!-- 转换规则与任务弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源转换规则与任务"
    width="1000px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="handleClose"
  >
    <div class="transformation-rule-content">
      <el-tabs v-model="activeTab">
        <!-- 数据源转换规则配置 Tab -->
        <el-tab-pane label="数据源转换规则配置" name="rule">
          <div style="margin-bottom: 20px;">
            <el-button size="small" type="primary" @click="onClickAddRule">新增转换规则</el-button>
          </div>
          <TableV2
            :defaultTableData="ruleList"
            :columns="[
              { prop: 'sequence', label: '序号', width: 80 },
              { prop: 'ruleName', label: '规则名称' },
              { prop: 'sourceDataType', label: '源数据类型' },
              { prop: 'targetDataType', label: '目标数据类型' },
              { prop: 'status', label: '状态' },
              { prop: 'createTime', label: '创建时间' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="350"
            :loading="loading"
            :buttons="[
              { label: '查看', type: 'info', code: 'view' },
              { label: '编辑', type: 'primary', code: 'edit' },
              { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
            ]"
            @click-button="onRuleAction"
          />
        </el-tab-pane>

        <!-- 数据源转换任务 Tab -->
        <el-tab-pane label="数据源转换任务" name="task">
          <div style="margin-bottom: 20px;">
            <el-button size="small" type="primary" @click="onClickAddTask">新增转换任务</el-button>
          </div>
          <TableV2
            :defaultTableData="taskList"
            :columns="[
              { prop: 'sequence', label: '序号', width: 80 },
              { prop: 'taskName', label: '任务名称' },
              { prop: 'relatedRule', label: '关联转换规则' },
              { prop: 'executionTime', label: '执行时间' },
              { prop: 'status', label: '状态' },
              { prop: 'createTime', label: '创建时间' }
            ]"
            :enable-toolbar="false"
            :enable-own-button="false"
            :enable-selection="false"
            height="350"
            :loading="loading"
            :buttons="[
              { label: '查看', type: 'info', code: 'view' },
              { label: '编辑', type: 'primary', code: 'edit' },
              { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
            ]"
            @click-button="onTaskAction"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新增/编辑转换规则弹窗 -->
    <Dialog
      v-model="showRuleFormDialog"
      :title="currentRule ? '编辑转换规则' : '新增转换规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetRuleForm"
      @click-confirm="onConfirmRule"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          ref="ruleFormRef"
          v-model="ruleForm"
          :props="[
            { label: '规则名称', prop: 'ruleName', type: 'text', required: true },
            { label: '源数据类型', prop: 'sourceDataType', type: 'select', required: true, options: dataTypeOptions },
            { label: '目标数据类型', prop: 'targetDataType', type: 'select', required: true, options: dataTypeOptions },
            { label: '转换规则', prop: 'transformationRule', type: 'textarea', required: true },
            { label: '是否启用', prop: 'enabled', type: 'switch' }
          ]"
          :rules="ruleFormRules"
          :enable-button="false"
        />
      </div>
    </Dialog>

    <!-- 新增/编辑转换任务弹窗 -->
    <Dialog
      v-model="showTaskFormDialog"
      :title="currentTask ? '编辑转换任务' : '新增转换任务'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetTaskForm"
      @click-confirm="onConfirmTask"
    >
      <div style="padding: 20px; min-height: 350px;">
        <Form
          ref="taskFormRef"
          v-model="taskForm"
          :props="[
            { label: '任务名称', prop: 'taskName', type: 'text', required: true },
            { label: '关联转换规则', prop: 'relatedRule', type: 'select', required: true, options: getEnabledRuleOptions() },
            { label: '任务描述', prop: 'description', type: 'textarea' },
            { label: '执行时间', prop: 'executionTime', type: 'datetime', datetype: 'time', placeholder: '请选择执行时间' },
            { label: '是否启用', prop: 'enabled', type: 'switch' }
          ]"
          :rules="taskFormRules"
          :enable-button="false"
        />
      </div>
    </Dialog>

    <!-- 查看详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      :title="detailTitle"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDetailDialog = false"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          v-if="currentDetail && activeTab === 'rule'"
          v-model="currentDetail"
          :props="[
            { label: '规则名称', prop: 'ruleName', type: 'text', disabled: true },
            { label: '源数据类型', prop: 'sourceDataType', type: 'text', disabled: true },
            { label: '目标数据类型', prop: 'targetDataType', type: 'text', disabled: true },
            { label: '转换规则', prop: 'transformationRule', type: 'textarea', disabled: true },
            { label: '状态', prop: 'status', type: 'text', disabled: true },
            { label: '创建时间', prop: 'createTime', type: 'text', disabled: true }
          ]"
          :enable-button="false"
        />
        <Form
          v-if="currentDetail && activeTab === 'task'"
          v-model="currentDetail"
          :props="[
            { label: '任务名称', prop: 'taskName', type: 'text', disabled: true },
            { label: '关联转换规则', prop: 'relatedRule', type: 'text', disabled: true },
            { label: '任务描述', prop: 'description', type: 'textarea', disabled: true },
            { label: '执行时间', prop: 'executionTime', type: 'text', disabled: true },
            { label: '状态', prop: 'status', type: 'text', disabled: true },
            { label: '创建时间', prop: 'createTime', type: 'text', disabled: true }
          ]"
          :enable-button="false"
        />
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const activeTab = ref('rule')
const loading = ref(false)
const formLoading = ref(false)

// 转换规则相关
const ruleList = ref<any[]>([])
const showRuleFormDialog = ref(false)
const ruleForm = ref<any>({})
const ruleFormRef = ref()
const currentRule = ref<any>(null)

// 转换任务相关
const taskList = ref<any[]>([])
const showTaskFormDialog = ref(false)
const taskForm = ref<any>({})
const taskFormRef = ref()
const currentTask = ref<any>(null)

// 详情相关
const showDetailDialog = ref(false)
const currentDetail = ref<any>(null)
const detailTitle = ref('')

// 缓存键
const RULE_STORAGE_KEY = 'transformationRules'
const TASK_STORAGE_KEY = 'transformationTasks'

// 数据类型选项
const dataTypeOptions = [
  { label: 'JSON', value: 'JSON' },
  { label: 'XML', value: 'XML' },
  { label: 'CSV', value: 'CSV' },
  { label: '数据库', value: '数据库' }
]

// 表单验证规则
const ruleFormRules = {
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  sourceDataType: [{ required: true, message: '请选择源数据类型', trigger: 'change' }],
  targetDataType: [{ required: true, message: '请选择目标数据类型', trigger: 'change' }],
  transformationRule: [{ required: true, message: '请输入转换规则', trigger: 'blur' }]
}

const taskFormRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  relatedRule: [{ required: true, message: '请选择关联转换规则', trigger: 'change' }]
}

// 获取启用的规则选项
const getEnabledRuleOptions = () => {
  return ruleList.value
    .filter(rule => rule.status === '开启')
    .map(rule => ({
      label: rule.ruleName,
      value: rule.ruleName
    }))
}

// 加载转换规则数据
const loadRulesFromCache = () => {
  try {
    const cached = localStorage.getItem(RULE_STORAGE_KEY)
    if (cached) {
      ruleList.value = JSON.parse(cached)
    } else {
      ruleList.value = []
    }
  } catch (error) {
    console.error('加载转换规则数据失败:', error)
    ruleList.value = []
  }
}

// 保存转换规则数据
const saveRulesToCache = () => {
  try {
    localStorage.setItem(RULE_STORAGE_KEY, JSON.stringify(ruleList.value))
  } catch (error) {
    console.error('保存转换规则数据失败:', error)
  }
}

// 加载转换任务数据
const loadTasksFromCache = () => {
  try {
    const cached = localStorage.getItem(TASK_STORAGE_KEY)
    if (cached) {
      taskList.value = JSON.parse(cached)
    } else {
      taskList.value = []
    }
  } catch (error) {
    console.error('加载转换任务数据失败:', error)
    taskList.value = []
  }
}

// 保存转换任务数据
const saveTasksToCache = () => {
  try {
    localStorage.setItem(TASK_STORAGE_KEY, JSON.stringify(taskList.value))
  } catch (error) {
    console.error('保存转换任务数据失败:', error)
  }
}

// 新增转换规则
const onClickAddRule = () => {
  currentRule.value = null
  resetRuleForm()
  showRuleFormDialog.value = true
}

// 重置转换规则表单
const resetRuleForm = () => {
  ruleForm.value = {
    ruleName: '',
    sourceDataType: '',
    targetDataType: '',
    transformationRule: '',
    enabled: true
  }
}

// 确认转换规则
const onConfirmRule = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true
      setTimeout(() => {
        if (currentRule.value) {
          // 编辑
          const index = ruleList.value.findIndex(item => item.id === currentRule.value.id)
          if (index !== -1) {
            ruleList.value[index] = {
              ...currentRule.value,
              ...ruleForm.value,
              status: ruleForm.value.enabled ? '开启' : '关闭',
              updateTime: new Date().toLocaleString('zh-CN')
            }
          }
          ElMessage.success('编辑转换规则成功')
        } else {
          // 新增
          const newRule = {
            id: `rule_${Date.now()}`,
            sequence: ruleList.value.length + 1,
            ...ruleForm.value,
            status: ruleForm.value.enabled ? '开启' : '关闭',
            createTime: new Date().toLocaleString('zh-CN')
          }
          ruleList.value.unshift(newRule)
          ElMessage.success('新增转换规则成功')
        }
        saveRulesToCache()
        showRuleFormDialog.value = false
        formLoading.value = false
      }, 1000)
    }
  })
}

// 转换规则操作
const onRuleAction = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      currentDetail.value = row
      detailTitle.value = '转换规则详情'
      showDetailDialog.value = true
      break
    case 'edit':
      currentRule.value = row
      ruleForm.value = { ...row, enabled: row.status === '开启' }
      showRuleFormDialog.value = true
      break
    case 'delete':
      const ruleIndex = ruleList.value.findIndex(item => item.id === row.id)
      if (ruleIndex !== -1) {
        ruleList.value.splice(ruleIndex, 1)
        saveRulesToCache()
        ElMessage.success('删除转换规则成功')
      }
      break
  }
}

// 新增转换任务
const onClickAddTask = () => {
  currentTask.value = null
  resetTaskForm()
  showTaskFormDialog.value = true
}

// 重置转换任务表单
const resetTaskForm = () => {
  taskForm.value = {
    taskName: '',
    relatedRule: '',
    description: '',
    executionTime: '',
    enabled: true
  }
}

// 确认转换任务
const onConfirmTask = () => {
  taskFormRef.value.validate((valid: boolean) => {
    if (valid) {
      formLoading.value = true
      setTimeout(() => {
        if (currentTask.value) {
          // 编辑
          const index = taskList.value.findIndex(item => item.id === currentTask.value.id)
          if (index !== -1) {
            taskList.value[index] = {
              ...currentTask.value,
              ...taskForm.value,
              status: taskForm.value.enabled ? '启用' : '禁用',
              updateTime: new Date().toLocaleString('zh-CN')
            }
          }
          ElMessage.success('编辑转换任务成功')
        } else {
          // 新增
          const newTask = {
            id: `task_${Date.now()}`,
            sequence: taskList.value.length + 1,
            ...taskForm.value,
            status: taskForm.value.enabled ? '启用' : '禁用',
            executionTime: taskForm.value.executionTime || '每日 10:30',
            createTime: new Date().toLocaleString('zh-CN')
          }
          taskList.value.unshift(newTask)
          ElMessage.success('新增转换任务成功')
        }
        saveTasksToCache()
        showTaskFormDialog.value = false
        formLoading.value = false
      }, 1000)
    }
  })
}

// 转换任务操作
const onTaskAction = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      currentDetail.value = row
      detailTitle.value = '转换任务详情'
      showDetailDialog.value = true
      break
    case 'edit':
      currentTask.value = row
      taskForm.value = { ...row, enabled: row.status === '启用' }
      showTaskFormDialog.value = true
      break
    case 'delete':
      const taskIndex = taskList.value.findIndex(item => item.id === row.id)
      if (taskIndex !== -1) {
        taskList.value.splice(taskIndex, 1)
        saveTasksToCache()
        ElMessage.success('删除转换任务成功')
      }
      break
  }
}



// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 初始化数据
const initData = () => {
  loadRulesFromCache()
  loadTasksFromCache()
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initData()
  }
})
</script>

<style lang="scss" scoped>
.transformation-rule-content {
  // 样式可以在这里添加
}
</style>
