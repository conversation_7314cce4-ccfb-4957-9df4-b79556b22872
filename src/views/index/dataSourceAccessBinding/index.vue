<!-- 数据源接入绑定页面 -->
<route>
{
meta: {
title:'数据集接入绑定',
ignoreLabel:false
}
}
</route>
<template>
  <div class="data-source-access-binding">
    <!-- 搜索区域 -->
    <Block title="数据集接入绑定" :enable-fixed-height="true" :enable-expand-content="true" :default-expand="false">
      <template #expand>
        <div class="search">
          <div class="search-row">
            <div class="search-fields">
              <el-form :model="searchForm" inline>
                <el-form-item label="分区名称" label-width="80px">
                  <el-input v-model="searchForm.partitionName" placeholder="请输入分区名称" size="small" style="width: 160px" clearable />
                </el-form-item>
                <el-form-item label="绑定状态" label-width="80px">
                  <el-select v-model="searchForm.bindingStatus" placeholder="请选择绑定状态" size="small" style="width: 160px" clearable>
                    <el-option label="全部" value="" />
                    <el-option label="启用" value="启用" />
                    <el-option label="禁用" value="禁用" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small" @click="onSearch">查询</el-button>
                  <el-button size="small" @click="onReset">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </template>

      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button size="small" type="primary" @click="onClickAdd">新建分区</el-button>
          <el-button size="small" type="primary" @click="onClickBatchImport">业务表数据导入</el-button>
          <el-button size="small" type="primary" @click="onClickBatchExport">业务表数据导出</el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <div style="width: 100%;">
        <BaseTableComp
          :data="tableData"
          :colData="tableColumns"
          :buttons="tableButtons"
          :current-page="pagination.currentPage"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :visible-setting="false"
          :loading="loading"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @clickButton="handleTableButtonClick"
        />
      </div>
    </Block>

    <!-- 新建分区弹窗 -->
    <Dialog
      v-model="showAddPartitionDialog"
      title="新增业务表分区管理"
      width="600px"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="onConfirmAddPartition"
      @click-cancel="onCloseAddPartitionDialog"
    >
      <div class="dialog-content">
        <el-form :model="partitionForm" label-width="120px">
          <el-form-item label="分区名称：" required>
            <el-input v-model="partitionForm.partitionName" placeholder="请输入分区名称" />
          </el-form-item>

          <el-form-item label="数据集名称：" required>
            <el-select
              v-model="partitionForm.datasetName"
              placeholder="请选择（从数据集里面获取）"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in datasetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="绑定业务表：" required>
            <el-select
              v-model="partitionForm.businessTables"
              placeholder="请选择（从业务表里面获取，可选择多个）"
              style="width: 100%"
              multiple
              clearable
            >
              <el-option
                v-for="item in businessTableOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否启用：">
            <el-switch v-model="partitionForm.enabled" />
          </el-form-item>
        </el-form>

        <div style="margin-top: 20px; text-align: right;">
          <el-button @click="onCloseAddPartitionDialog">取消</el-button>
          <el-button
            type="primary"
            :disabled="!isRequiredFieldsComplete"
            @click="onDeduplicateBusinessTable"
          >
            业务表去重
          </el-button>
          <el-button
            type="primary"
            :disabled="!isRequiredFieldsComplete"
            @click="onValidateBusinessTableData"
          >
            业务表数据校验
          </el-button>
          <el-button type="primary" @click="onConfirmAddPartition">确定</el-button>
        </div>
      </div>
    </Dialog>

    <!-- 批量导入弹窗 -->
    <Dialog
      v-model="showImportDialog"
      title="业务表数据导入"
      width="600px"
      :destroy-on-close="true"
      :loading="importLoading"
      loading-text="导入中"
      confirm-text="上传"
      @click-confirm="uploadFile"
    >
      <div class="import-content">
        <div class="import-header">
          <el-icon><Upload /></el-icon>
          <span>导入须知</span>
        </div>

        <div class="import-steps">
          <div class="step-title">操作流程：</div>
          <div class="steps">
            <div class="step">
              <span class="step-number">1</span>
              <span class="step-text">下载模板</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">2</span>
              <span class="step-text">填写表格</span>
            </div>
            <div class="step-divider"></div>
            <div class="step">
              <span class="step-number">3</span>
              <span class="step-text">上传表格</span>
            </div>
          </div>
        </div>

        <div class="template-download">
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            导入模板下载
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            class="upload-demo"
            drag
            :file-list="importFileList"
            :before-upload="handleFileChange"
            :on-remove="handleFileRemove"
            :on-change="handleFileSelect"
            :limit="1"
            accept=".xlsx,.xls"
            :auto-upload="false"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              点击或将文件拖拽到这里上传
            </div>
            <template #tip>
              <div class="el-upload__tip">
                导入模式为业务表中已有数据更新，业务表中不含数据新增，仅支持后缀名为xlsx文件
              </div>
            </template>
          </el-upload>

          <!-- 显示选中的文件名 -->
          <div v-if="selectedFileName" class="selected-file">
            <div class="file-name">{{ selectedFileName }}</div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 数据清洗规则弹窗 -->
    <DataCleaningTaskDialog
      v-model="showDataCleaningDialog"
      :edit-data="currentRow"
    />

    <!-- 数据转换规则弹窗 -->
    <DataTransformRuleDialog
      v-model="showDataTransformDialog"
      :edit-data="currentRow"
      @confirm="handleDataTransformConfirm"
    />

    <!-- 数据备份与恢复弹窗 -->
    <DataBackupRecoveryDialog
      v-model="showDataBackupDialog"
      :edit-data="currentRow"
    />

    <!-- 权限管理弹窗 -->
    <AccessPermissionDialog
      v-model="showAccessPermissionDialog"
      :edit-data="currentRow"
    />

    <!-- 详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      title="查看详情"
      width="800px"
      :destroy-on-close="true"
    >
      <div v-if="currentRow" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="分区名称">{{ currentRow.partitionName }}</el-descriptions-item>
          <el-descriptions-item label="数据集名称">{{ currentRow.datasetName }}</el-descriptions-item>
          <el-descriptions-item label="绑定业务表">{{ currentRow.businessTable }}</el-descriptions-item>
          <el-descriptions-item label="绑定状态">
            <el-tag :type="currentRow.bindingStatus === '启用' ? 'success' : 'danger'">
              {{ currentRow.bindingStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ currentRow.creator }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentRow.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </Dialog>

    <!-- 编辑弹窗 -->
    <Dialog
      v-model="showEditDialog"
      title="编辑分区信息"
      width="600px"
      :destroy-on-close="true"
      confirm-text="保存"
      @click-confirm="handleEditConfirm"
    >
      <div v-if="currentRow" class="edit-content">
        <el-form :model="editForm" label-width="120px">
          <el-form-item label="分区名称：" required>
            <el-input v-model="editForm.partitionName" placeholder="请输入分区名称" />
          </el-form-item>
          <el-form-item label="数据集名称：" required>
            <el-select v-model="editForm.datasetName" placeholder="请选择数据集名称" style="width: 100%">
              <el-option
                v-for="item in datasetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="绑定业务表：" required>
            <el-input v-model="editForm.businessTable" placeholder="请输入绑定业务表" />
          </el-form-item>
          <el-form-item label="绑定状态：">
            <el-switch
              v-model="editForm.bindingStatus"
              active-text="启用"
              inactive-text="禁用"
              :active-value="'启用'"
              :inactive-value="'禁用'"
            />
          </el-form-item>
        </el-form>
      </div>
    </Dialog>

  </div>
</template>

<script setup lang="ts" name="DataSourceAccessBinding">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Download, UploadFilled, ArrowDown, Refresh } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import DataCleaningTaskDialog from '../dataSourceManagement/components/DataCleaningTaskDialog.vue'
import DataTransformRuleDialog from './components/DataTransformRuleDialog.vue'
import DataBackupRecoveryDialog from './components/DataBackupRecoveryDialog.vue'
import AccessPermissionDialog from './components/AccessPermissionDialog.vue'

// 搜索表单
const searchForm = ref({
  partitionName: '',
  bindingStatus: ''
})

// 表格数据
const tableData = ref<any[]>([])

// 加载状态
const loading = ref(false)

// 缓存键
const TABLE_DATA_STORAGE_KEY = 'dataSourceAccessBinding_tableData'

// 保存表格数据到localStorage
const saveTableDataToCache = () => {
  try {
    // 保存原始数据，而不是过滤后的数据
    localStorage.setItem(TABLE_DATA_STORAGE_KEY, JSON.stringify(originalTableData.value))
    console.log('表格数据已保存到缓存')
  } catch (error) {
    console.error('保存表格数据失败:', error)
  }
}

// 从localStorage加载表格数据
const loadTableDataFromCache = () => {
  try {
    const cached = localStorage.getItem(TABLE_DATA_STORAGE_KEY)
    if (cached) {
      const cachedData = JSON.parse(cached)
      if (Array.isArray(cachedData) && cachedData.length > 0) {
        originalTableData.value = cachedData
        tableData.value = [...cachedData] // 复制到显示数据
        console.log('从缓存加载表格数据:', cachedData.length, '条')
        return true
      }
    }
  } catch (error) {
    console.error('加载缓存数据失败:', error)
  }
  return false
}

// 表格列配置
const tableColumns = ref([
  { field: 'partitionName', title: '分区名称' },
  { field: 'datasetName', title: '数据集名称' },
  { field: 'businessTable', title: '绑定业务表' },
  { field: 'bindingStatus', title: '绑定状态' },
  { field: 'creator', title: '创建人' },
  { field: 'createTime', title: '创建时间' }
])

// 表格操作按钮配置
const tableButtons = ref([
  { type: 'primary', code: 'detail', title: '详情', label: '详情', more: false },
  { type: 'primary', code: 'edit', title: '修改', label: '修改', more: false },
  { type: 'danger', code: 'delete', title: '删除', label: '删除', more: false },
  { type: 'primary', code: 'clean', title: '数据清洗规则', label: '数据清洗规则', more: true },
  { type: 'primary', code: 'transform', title: '数据转换规则', label: '数据转换规则', more: true },
  { type: 'primary', code: 'backup', title: '数据备份与恢复', label: '数据备份与恢复', more: true },
  { type: 'primary', code: 'permission', title: '权限管理', label: '权限管理', more: true }
])

// 调试：打印按钮配置
console.log('按钮配置:', tableButtons.value)
console.log('更多按钮:', tableButtons.value.filter(f => f.more))

// 为操作列设置固定宽度
const operationColumnWidth = 280

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 3
})

// 新建分区弹窗
const showAddPartitionDialog = ref(false)
const partitionForm = ref({
  partitionName: '',
  datasetName: '',
  businessTables: [],
  enabled: true
})

// 数据集选项
const datasetOptions = ref([
  { label: '数据集1', value: 'dataset1' },
  { label: '数据集2', value: 'dataset2' },
  { label: '数据集3', value: 'dataset3' }
])

// 业务表选项
const businessTableOptions = ref([
  { label: '业务表1', value: 'table1' },
  { label: '业务表2', value: 'table2' },
  { label: '业务表3', value: 'table3' },
  { label: '业务表4', value: 'table4' },
  { label: '业务表5', value: 'table5' }
])

// 计算属性：判断必填字段是否完成
const isRequiredFieldsComplete = computed(() => {
  return partitionForm.value.partitionName &&
         partitionForm.value.datasetName &&
         partitionForm.value.businessTables.length > 0
})

// 导入相关
const showImportDialog = ref(false)
const importLoading = ref(false)

// 数据清洗弹窗相关
const showDataCleaningDialog = ref(false)
const currentRow = ref(null)

// 数据转换弹窗相关
const showDataTransformDialog = ref(false)

// 数据备份弹窗相关
const showDataBackupDialog = ref(false)

// 权限管理弹窗相关
const showAccessPermissionDialog = ref(false)

// 详情弹窗相关
const showDetailDialog = ref(false)

// 编辑弹窗相关
const showEditDialog = ref(false)
const editForm = ref({
  partitionName: '',
  datasetName: '',
  businessTable: '',
  bindingStatus: '启用'
})

const importFileList = ref<any[]>([])
const selectedFileName = ref('')

// 原始表格数据（用于搜索过滤）
const originalTableData = ref<any[]>([])

// 搜索
const onSearch = () => {
  console.log('执行搜索，搜索条件:', searchForm.value)

  // 重置到第一页
  pagination.currentPage = 1

  // 从原始数据中过滤
  let filteredData = [...originalTableData.value]

  // 按分区名称过滤
  if (searchForm.value.partitionName.trim()) {
    filteredData = filteredData.filter(item =>
      item.partitionName.includes(searchForm.value.partitionName.trim())
    )
  }

  // 按绑定状态过滤
  if (searchForm.value.bindingStatus) {
    filteredData = filteredData.filter(item =>
      item.bindingStatus === searchForm.value.bindingStatus
    )
  }

  // 更新表格数据
  tableData.value = filteredData
  pagination.total = filteredData.length

  console.log('搜索结果:', filteredData.length, '条数据')
}

// 重置
const onReset = () => {
  searchForm.value = {
    partitionName: '',
    bindingStatus: ''
  }
  // 重置后显示所有数据
  tableData.value = [...originalTableData.value]
  pagination.total = originalTableData.value.length
  pagination.currentPage = 1

  console.log('重置搜索，显示所有数据:', originalTableData.value.length, '条')
}

// 新建分区
const onClickAdd = () => {
  // 重置表单数据
  partitionForm.value = {
    partitionName: '',
    datasetName: '',
    businessTables: [],
    enabled: true
  }
  showAddPartitionDialog.value = true
}

// 关闭新建分区弹窗
const onCloseAddPartitionDialog = () => {
  showAddPartitionDialog.value = false
  // 重置表单
  partitionForm.value = {
    partitionName: '',
    datasetName: '',
    businessTables: [],
    enabled: true
  }
}

// 确认新建分区
const onConfirmAddPartition = () => {
  if (!partitionForm.value.partitionName) {
    ElMessage.warning('请输入分区名称')
    return
  }
  if (!partitionForm.value.datasetName) {
    ElMessage.warning('请选择数据集名称')
    return
  }
  if (!partitionForm.value.businessTables.length) {
    ElMessage.warning('请选择绑定业务表')
    return
  }

  // 创建新的分区数据
  const newPartition = {
    id: Date.now(), // 使用时间戳作为临时ID
    partitionName: partitionForm.value.partitionName,
    datasetName: partitionForm.value.datasetName,
    businessTable: partitionForm.value.businessTables.join(', '), // 多个业务表用逗号分隔
    bindingStatus: partitionForm.value.enabled ? '启用' : '禁用',
    creator: '当前用户', // 这里应该从用户信息中获取
    createTime: new Date().toLocaleDateString('zh-CN')
  }

  // 添加到原始数据和显示数据中
  originalTableData.value.unshift(newPartition) // 添加到原始数据开头
  tableData.value.unshift(newPartition) // 添加到显示数据开头

  // 更新分页总数
  pagination.total = tableData.value.length

  // 保存到localStorage
  saveTableDataToCache()

  // TODO: 这里应该调用API保存数据
  // try {
  //   const response = await dataSourceAccessBindingApi.create(newPartition)
  //   // 保存成功后刷新数据
  //   loadTableData()
  // } catch (error) {
  //   ElMessage.error('保存失败，请重试')
  //   return
  // }

  ElMessage.success('新建分区成功')
  onCloseAddPartitionDialog()

  console.log('新建分区数据:', newPartition)
  console.log('当前表格数据:', tableData.value)
}

// 业务表去重
const onDeduplicateBusinessTable = () => {
  ElMessage.success('去重成功')
}

// 业务表数据校验
const onValidateBusinessTableData = () => {
  ElMessage.success('校验成功')
}

// 表格按钮点击事件处理
const handleTableButtonClick = (data: any) => {
  const { btn, scope } = data
  const code = btn.code
  // 修复：scope 就是行数据，不需要 scope.row
  const row = scope

  console.log('按钮点击事件:', { code, row })

  switch (code) {
    case 'detail':
      console.log('查看详情，行数据:', row)
      currentRow.value = row
      showDetailDialog.value = true
      break
    case 'edit':
      console.log('编辑，行数据:', row)
      currentRow.value = row
      // 回显数据到编辑表单
      editForm.value = {
        partitionName: row.partitionName || '',
        datasetName: row.datasetName || '',
        businessTable: row.businessTable || '',
        bindingStatus: row.bindingStatus || '启用'
      }
      showEditDialog.value = true
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除分区"${row.partitionName}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从原始数据中删除
        const originalIndex = originalTableData.value.findIndex(item => item.id === row.id)
        if (originalIndex !== -1) {
          originalTableData.value.splice(originalIndex, 1)
        }

        // 从显示数据中删除
        const displayIndex = tableData.value.findIndex(item => item.id === row.id)
        if (displayIndex !== -1) {
          tableData.value.splice(displayIndex, 1)
          // 更新分页总数
          pagination.total = tableData.value.length
          // 保存到缓存
          saveTableDataToCache()
          ElMessage.success('删除成功')
        } else {
          ElMessage.error('未找到要删除的数据')
        }

        // TODO: 这里应该调用删除API
        // try {
        //   await dataSourceAccessBindingApi.delete(row.id)
        //   loadTableData()
        // } catch (error) {
        //   ElMessage.error('删除失败，请重试')
        // }
      }).catch(() => {
        ElMessage.info('已取消删除')
      })
      break
    case 'clean':
      console.log('数据清洗规则，行数据:', row)
      currentRow.value = row
      showDataCleaningDialog.value = true
      break
    case 'transform':
      console.log('数据转换规则，行数据:', row)
      currentRow.value = row
      showDataTransformDialog.value = true
      break
    case 'backup':
      console.log('数据备份与恢复，行数据:', row)
      currentRow.value = row
      showDataBackupDialog.value = true
      break
    case 'permission':
      console.log('权限管理，行数据:', row)
      currentRow.value = row
      showAccessPermissionDialog.value = true
      break
    default:
      ElMessage.info(`未知操作：${code}`)
  }
}

// 批量导入
const onClickBatchImport = () => {
  showImportDialog.value = true
  importFileList.value = []
  selectedFileName.value = ''
}

// 批量导出 - 直接导出，与数据集管理保持一致
const onClickBatchExport = async () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据源接入绑定数据')

    // 设置表头
    const headers = [
      { header: '序号', key: 'id', width: 10 },
      { header: '名称', key: 'name', width: 20 },
      { header: '类型', key: 'type', width: 15 },
      { header: '主机地址', key: 'host', width: 20 },
      { header: '端口', key: 'port', width: 10 },
      { header: '数据库', key: 'database', width: 20 },
      { header: '状态', key: 'status', width: 15 },
      { header: '创建时间', key: 'createTime', width: 20 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加数据
    tableData.value.forEach((row, index) => {
      worksheet.addRow({
        id: index + 1,
        name: row.name,
        type: row.type,
        host: row.host,
        port: row.port,
        database: row.database,
        status: row.status,
        createTime: row.createTime
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const fileName = `数据源接入绑定数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)

    ElMessage.success(`成功导出 ${tableData.value.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 数据转换确认处理
const handleDataTransformConfirm = (data: any) => {
  console.log('数据转换规则配置:', data)
  // 弹窗组件已经显示成功提示，这里不需要重复显示
}

// 编辑确认处理
const handleEditConfirm = () => {
  if (!editForm.value.partitionName) {
    ElMessage.warning('请输入分区名称')
    return
  }
  if (!editForm.value.datasetName) {
    ElMessage.warning('请选择数据集名称')
    return
  }
  if (!editForm.value.businessTable) {
    ElMessage.warning('请输入绑定业务表')
    return
  }

  // 更新原始数据
  const originalIndex = originalTableData.value.findIndex(item => item.id === currentRow.value?.id)
  if (originalIndex !== -1) {
    originalTableData.value[originalIndex] = {
      ...originalTableData.value[originalIndex],
      ...editForm.value
    }
  }

  // 更新显示数据
  const displayIndex = tableData.value.findIndex(item => item.id === currentRow.value?.id)
  if (displayIndex !== -1) {
    tableData.value[displayIndex] = {
      ...tableData.value[displayIndex],
      ...editForm.value
    }
    // 保存到缓存
    saveTableDataToCache()
    ElMessage.success('编辑成功')
    showEditDialog.value = false
  } else {
    ElMessage.error('未找到要编辑的数据')
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1 // 重置到第一页
  // 如果有搜索条件，重新执行搜索；否则加载所有数据
  if (searchForm.value.partitionName || searchForm.value.bindingStatus) {
    onSearch()
  } else {
    tableData.value = [...originalTableData.value]
    pagination.total = originalTableData.value.length
  }
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  // 分页切换时不需要重新加载数据，因为我们使用前端分页
}

const handleSortChange = (data: any) => {
  console.log('排序变化:', data)
}

// 下载模板
const downloadTemplate = async () => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('数据源接入绑定模板')

    // 设置表头
    const headers = [
      { header: '名称', key: 'name', width: 20 },
      { header: '类型', key: 'type', width: 15 },
      { header: '主机地址', key: 'host', width: 20 },
      { header: '端口', key: 'port', width: 10 },
      { header: '数据库', key: 'database', width: 20 },
      { header: '状态', key: 'status', width: 15 }
    ]

    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加示例数据
    worksheet.addRow({
      name: 'MySQL-示例',
      type: 'MySQL',
      host: '*************',
      port: '3306',
      database: 'example_db',
      status: '已连接'
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    
    // 下载文件
    const fileName = `数据源接入绑定模板_${new Date().toISOString().slice(0, 10)}.xlsx`
    saveAs(blob, fileName)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 处理文件选择
const handleFileSelect = (file: any) => {
  console.log('文件选择:', file)
  selectedFileName.value = file.name
  ElMessage.success('文件选择成功，点击上传按钮开始导入')
}

// 处理文件上传前验证
const handleFileChange = (file: any) => {
  console.log('文件上传前验证:', file)
  return false // 阻止自动上传
}

// 处理文件移除
const handleFileRemove = () => {
  importFileList.value = []
  selectedFileName.value = ''
}

// 上传并解析Excel文件
const uploadFile = async () => {
  console.log('开始上传文件:', importFileList.value, selectedFileName.value)

  if (importFileList.value.length === 0 || !selectedFileName.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 获取文件对象
  const file = importFileList.value[0].raw || importFileList.value[0]
  console.log('获取到的文件对象:', file)

  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
    ElMessage.error('请上传Excel文件(.xlsx或.xls格式)')
    return
  }

  importLoading.value = true

  try {
    const buffer = await file.arrayBuffer()
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.load(buffer)

    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }

    const importData: any[] = []
    const errors: string[] = []

    // 从第二行开始读取数据（第一行是表头）
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return // 跳过表头

      const values = row.values as any[]
      const name = values[1]?.toString()?.trim()
      const type = values[2]?.toString()?.trim()
      const host = values[3]?.toString()?.trim()
      const port = values[4]?.toString()?.trim()
      const database = values[5]?.toString()?.trim()
      const status = values[6]?.toString()?.trim()

      if (!name || !type || !host || !port || !database) {
        errors.push(`第${rowNumber}行：必填字段不能为空`)
        return
      }

      importData.push({
        id: Date.now() + Math.random(),
        name,
        type,
        host,
        port,
        database,
        status: status || '未连接',
        createTime: new Date().toLocaleString()
      })
    })

    if (errors.length > 0) {
      ElMessage.error(`导入失败：${errors.join('；')}`)
      return
    }

    if (importData.length === 0) {
      ElMessage.warning('没有找到有效的数据行')
      return
    }

    // 将导入的数据添加到表格中
    tableData.value.push(...importData)
    pagination.total = tableData.value.length

    ElMessage.success(`成功导入 ${importData.length} 条数据`)
    showImportDialog.value = false
    
    // 重置文件列表
    importFileList.value = []
    selectedFileName.value = ''

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请检查文件格式')
  } finally {
    importLoading.value = false
  }
}



// 全局函数，供表格操作按钮调用
;(window as any).handleDetail = (id: number) => {
  ElMessage.info(`查看详情：ID ${id}`)
}

;(window as any).handleEdit = (id: number) => {
  ElMessage.info(`编辑：ID ${id}`)
}

;(window as any).handleDelete = (id: number) => {
  ElMessage.info(`删除：ID ${id}`)
}

;(window as any).handleTest = (id: number) => {
  ElMessage.success(`连接测试成功：ID ${id}`)
}

;(window as any).handleSync = (id: number) => {
  ElMessage.success(`数据同步成功：ID ${id}`)
}

;(window as any).handleBackup = (id: number) => {
  ElMessage.success(`备份配置成功：ID ${id}`)
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // TODO: 替换为实际的API调用
    // 示例API集成代码：
    // import { dataSourceAccessBindingApi } from '@/api/dataSourceAccessBinding'
    // const response = await dataSourceAccessBindingApi.getList({
    //   page: pagination.currentPage,
    //   size: pagination.pageSize,
    //   ...searchForm.value
    // })
    // tableData.value = response.data.records || []
    // pagination.total = response.data.total || 0

    console.log('正在加载数据集接入绑定数据...')

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 首先尝试从缓存加载数据
    const hasCache = loadTableDataFromCache()

    // 如果没有缓存数据，初始化一些测试数据
    if (!hasCache) {
      console.log('没有缓存数据，初始化测试数据')
      const initialData = [
        {
          id: 1,
          partitionName: '分区1',
          datasetName: '数据集1',
          businessTable: '业务表1',
          bindingStatus: '启用',
          creator: '张三',
          createTime: '2025.7.25'
        },
        {
          id: 2,
          partitionName: '分区2',
          datasetName: '数据集2',
          businessTable: '业务表2',
          bindingStatus: '禁用',
          creator: '李四',
          createTime: '2025.7.25'
        },
        {
          id: 3,
          partitionName: '分区3',
          datasetName: '数据集3',
          businessTable: '业务表3',
          bindingStatus: '启用',
          creator: '王五',
          createTime: '2025.7.25'
        }
      ]

      originalTableData.value = initialData
      tableData.value = [...initialData]

      // 保存初始数据到缓存
      saveTableDataToCache()
    }

    // 更新分页总数
    pagination.total = tableData.value.length

    console.log('数据加载完成，当前数据条数:', tableData.value.length)
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadTableData()
}

onMounted(() => {
  // 页面加载时获取数据
  loadTableData()
})
</script>

<style scoped>
.data-source-access-binding {
  padding: 20px;
}

.search {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-fields {
  flex: 1;
}

.top-buttons .el-button {
  margin-right: 10px;
}

.dialog-content {
  padding: 20px;
}

/* 导入弹窗样式 */
.import-content {
  padding: 20px;
}

.import-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.import-header .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.import-steps {
  margin-bottom: 20px;
}

.step-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.steps {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
}

.step-text {
  font-size: 14px;
  color: #666;
}

.step-divider {
  width: 60px;
  height: 2px;
  background: #e4e7ed;
  margin: 0 20px;
  margin-bottom: 20px;
}

.template-download {
  text-align: center;
  margin-bottom: 20px;
}

.upload-area {
  margin-top: 20px;
}

.selected-file {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-name {
  color: #409eff;
  font-weight: bold;
}
</style>
