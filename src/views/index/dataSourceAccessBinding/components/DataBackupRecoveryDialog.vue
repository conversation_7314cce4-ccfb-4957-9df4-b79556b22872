<!-- 数据备份与恢复弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集备份与恢复"
    :destroy-on-close="true"
    width="800px"
    confirm-text="保存"
    cancel-text="取消"
    @click-confirm="saveBackupRuleToCache"
    @click-cancel="handleClose"
  >
    <div class="dialog-content" style="padding: 20px;">
      <!-- 数据集备份规则 -->
      <div class="section-title" style="font-size: 16px; font-weight: bold; margin-bottom: 20px; color: #333;">数据集备份规则</div>
      <div class="form-section">
        <el-form :model="backupRuleForm" label-width="180px">
          <el-form-item label="数据备份开始时间：">
            <el-date-picker
              v-model="backupRuleForm.backupStartTime"
              type="datetime"
              placeholder="请选择日期时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="全量备份频率：">
            <el-select v-model="backupRuleForm.fullBackupFrequency" style="width: 100%">
              <el-option label="每小时" value="每小时" />
              <el-option label="每天" value="每天" />
              <el-option label="每周" value="每周" />
              <el-option label="每月" value="每月" />
            </el-select>
          </el-form-item>

          <el-form-item label="增量备份频率：">
            <el-select v-model="backupRuleForm.incrementalBackupFrequency" style="width: 100%">
              <el-option label="每小时" value="每小时" />
              <el-option label="每天" value="每天" />
              <el-option label="每周" value="每周" />
              <el-option label="每月" value="每月" />
            </el-select>
          </el-form-item>

          <el-form-item label="数据清理策略：">
            <el-select v-model="backupRuleForm.dataCleanupPolicy" style="width: 100%">
              <el-option label="保留7天" value="保留7天" />
              <el-option label="保留30天" value="保留30天" />
              <el-option label="保留90天" value="保留90天" />
              <el-option label="保留1年" value="保留1年" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据集恢复规则 -->
      <div class="section-title" style="font-size: 16px; font-weight: bold; margin: 30px 0 20px 0; color: #333;">数据集恢复规则</div>
      <div class="form-section">
        <el-form :model="backupRuleForm" label-width="180px">
          <el-form-item label="恢复到时间：">
            <el-date-picker
              v-model="backupRuleForm.recoveryToTime"
              type="datetime"
              placeholder="请选择日期时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="恢复执行时间：">
            <el-date-picker
              v-model="backupRuleForm.recoveryExecutionTime"
              type="datetime"
              placeholder="请选择日期时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 备份规则表单
const backupRuleForm = ref({
  backupStartTime: '',
  fullBackupFrequency: '每小时',
  incrementalBackupFrequency: '每小时',
  dataCleanupPolicy: '保留30天',
  recoveryToTime: '',
  recoveryExecutionTime: ''
})

// 缓存键
const STORAGE_KEY = 'dataSourceAccessBinding_backupRule'
const ROW_CONFIG_STORAGE_KEY = 'dataBackupRowConfigs'

// 加载备份规则配置
const loadBackupRuleFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      backupRuleForm.value = {
        backupStartTime: config.backupStartTime || '',
        fullBackupFrequency: config.fullBackupFrequency || '每小时',
        incrementalBackupFrequency: config.incrementalBackupFrequency || '每小时',
        dataCleanupPolicy: config.dataCleanupPolicy || '保留30天',
        recoveryToTime: config.recoveryToTime || '',
        recoveryExecutionTime: config.recoveryExecutionTime || ''
      }
    }
  } catch (error) {
    console.error('加载数据集备份规则失败:', error)
  }
}

// 基于行ID查找已保存的配置
const findRowConfigByRowId = (rowId: number) => {
  try {
    const cached = localStorage.getItem(ROW_CONFIG_STORAGE_KEY)
    if (!cached) return null

    const configs = JSON.parse(cached)
    return configs.find((config: any) => config.rowId === rowId)
  } catch (error) {
    console.error('查找备份规则行配置失败:', error)
    return null
  }
}

// 保存行配置数据
const saveRowConfigToCache = (rowData: any) => {
  if (!rowData?.id) {
    console.error('缺少行ID，无法保存备份规则配置')
    return
  }

  try {
    const cached = localStorage.getItem(ROW_CONFIG_STORAGE_KEY)
    let configs = []

    if (cached) {
      configs = JSON.parse(cached)
    }

    const rowId = rowData.id
    const existingIndex = configs.findIndex((config: any) => config.rowId === rowId)

    const configData = {
      rowId: rowId,
      partitionName: rowData.partitionName,
      datasetName: rowData.datasetName,
      businessTable: rowData.businessTable,
      bindingStatus: rowData.bindingStatus,
      backupRuleForm: backupRuleForm.value,
      createTime: existingIndex >= 0 ? configs[existingIndex].createTime : new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }

    if (existingIndex >= 0) {
      // 更新现有配置
      configs[existingIndex] = configData
      console.log('更新备份规则配置:', configData)
    } else {
      // 新增配置
      configs.push(configData)
      console.log('新增备份规则配置:', configData)
    }

    localStorage.setItem(ROW_CONFIG_STORAGE_KEY, JSON.stringify(configs))
  } catch (error) {
    console.error('保存备份规则行配置失败:', error)
  }
}

// 保存备份规则配置
const saveBackupRuleToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(backupRuleForm.value))

    // 同时保存行配置
    if (props.editData) {
      saveRowConfigToCache(props.editData)
    }

    ElMessage.success('数据集备份与恢复规则保存成功')
    visible.value = false
  } catch (error) {
    console.error('保存数据集备份规则失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理编辑数据的函数
const handleEditData = (editData: any) => {
  if (!editData) {
    return
  }

  console.log('数据备份与恢复 - 处理编辑数据:', editData)

  // 首先尝试基于行ID查找已保存的配置
  if (editData.id) {
    const savedConfig = findRowConfigByRowId(editData.id)
    if (savedConfig) {
      console.log('找到已保存的备份规则配置:', savedConfig)
      // 使用已保存的配置数据回显到表单
      if (savedConfig.backupRuleForm) {
        backupRuleForm.value = {
          ...backupRuleForm.value,
          ...savedConfig.backupRuleForm
        }
      }
      // 恢复相关字段已经包含在 backupRuleForm 中
      console.log('回显已保存的备份规则配置:', backupRuleForm.value)
      return
    }
  }

  // 如果没有找到已保存的配置，使用默认配置
  console.log('未找到已保存配置，使用默认备份规则配置')

  // 根据表格行数据生成智能描述和配置
  if (editData.partitionName && editData.datasetName) {
    console.log(`数据备份与恢复 - 为${editData.partitionName}的${editData.datasetName}配置备份策略`)

    // 根据行数据设置特定的备份策略到表单字段
    if (editData.bindingStatus === '启用') {
      // 为启用状态的数据源设置更频繁的备份
      console.log('数据备份与恢复 - 设置启用状态的备份策略（高频备份）')
      backupRuleForm.value.fullBackupFrequency = '每小时'
      backupRuleForm.value.incrementalBackupFrequency = '每30分钟'
      backupRuleForm.value.dataCleanupPolicy = '保留30天'
    } else {
      // 为禁用状态的数据源设置基础备份策略
      console.log('数据备份与恢复 - 设置禁用状态的备份策略（基础备份）')
      backupRuleForm.value.fullBackupFrequency = '每天'
      backupRuleForm.value.incrementalBackupFrequency = '每小时'
      backupRuleForm.value.dataCleanupPolicy = '保留7天'
    }

    // 设置默认的备份开始时间
    const now = new Date()
    backupRuleForm.value.backupStartTime = now.toTimeString().slice(0, 5) // HH:MM 格式

    console.log('设置备份规则表单数据:', backupRuleForm.value)
  }
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadBackupRuleFromCache()

    // 处理编辑数据
    if (props.editData) {
      handleEditData(props.editData)
    }
  }
})

// 监听编辑数据变化
watch(() => props.editData, (newEditData) => {
  if (props.modelValue && newEditData) {
    handleEditData(newEditData)
  }
})
</script>

<style lang="scss" scoped>
.dialog-content {
  .section-title {
    border-left: 4px solid #409eff;
    padding-left: 10px;
  }
  
  .form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}
</style>
