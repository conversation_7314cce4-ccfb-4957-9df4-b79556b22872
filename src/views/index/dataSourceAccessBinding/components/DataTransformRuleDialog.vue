<!-- 数据转换规则弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据转换规则设置"
    width="800px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    confirm-text="确定"
    cancel-text="取消"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="dialog-content">
      <el-form :model="form" label-width="140px">
        <el-form-item label="业务数据表：" required>
          <el-select
            v-model="form.sourceTable"
            placeholder="请选择"
            style="width: 100%;"
            @change="onSourceTableChange"
          >
            <el-option
              v-for="table in businessTables"
              :key="table.value"
              :label="table.label"
              :value="table.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="目标业务数据表：" required>
          <el-select
            v-model="form.targetTable"
            placeholder="请选择"
            style="width: 100%;"
            @change="onTargetTableChange"
          >
            <el-option
              v-for="table in businessTables"
              :key="table.value"
              :label="table.label"
              :value="table.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="数据转换规则类型：" required>
          <el-radio-group v-model="form.ruleType">
            <el-radio label="mapping">字段映射</el-radio>
            <el-radio label="filter">数据过滤</el-radio>
            <el-radio label="aggregate">数据聚合</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="规则描述：" required>
          <el-input
            v-model="form.ruleDescription"
            type="textarea"
            :rows="4"
            placeholder="请输入"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="是否启用：">
          <el-checkbox v-model="form.enabled">启用</el-checkbox>
        </el-form-item>
      </el-form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  sourceTable: '',
  targetTable: '',
  ruleType: 'mapping',
  ruleDescription: '',
  enabled: true
})

// 业务表选项（模拟真实的业务表名称）
const businessTables = ref([
  { label: '用户信息表(user_info)', value: 'user_info' },
  { label: '订单数据表(order_data)', value: 'order_data' },
  { label: '产品信息表(product_info)', value: 'product_info' },
  { label: '客户关系表(customer_relation)', value: 'customer_relation' },
  { label: '财务数据表(financial_data)', value: 'financial_data' },
  { label: '库存管理表(inventory_management)', value: 'inventory_management' },
  { label: '销售记录表(sales_record)', value: 'sales_record' },
  { label: '员工档案表(employee_profile)', value: 'employee_profile' },
  { label: '供应商信息表(supplier_info)', value: 'supplier_info' },
  { label: '物流跟踪表(logistics_tracking)', value: 'logistics_tracking' }
])

// 缓存键
const STORAGE_KEY = 'dataTransformRules'

// 源表变化处理
const onSourceTableChange = (value: string) => {
  // 可以在这里添加源表变化的业务逻辑
}

// 目标表变化处理
const onTargetTableChange = (value: string) => {
  // 可以在这里添加目标表变化的业务逻辑
}

// 验证必填项
const validateRequiredFields = () => {
  const errors = []
  
  if (!form.value.sourceTable) {
    errors.push('业务数据表不能为空')
  }
  
  if (!form.value.targetTable) {
    errors.push('目标业务数据表不能为空')
  }
  
  if (form.value.sourceTable === form.value.targetTable) {
    errors.push('业务数据表和目标业务数据表不能相同')
  }
  
  if (!form.value.ruleDescription.trim()) {
    errors.push('规则描述不能为空')
  }
  
  return errors
}

// 基于行ID查找已保存的配置
const findConfigByRowId = (rowId: number) => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (!cached) return null

    const configs = JSON.parse(cached)
    return configs.find((config: any) => config.rowId === rowId)
  } catch (error) {
    console.error('查找转换规则配置失败:', error)
    return null
  }
}

// 保存配置到缓存
const saveConfigToCache = () => {
  if (!props.editData?.id) {
    console.error('缺少行ID，无法保存配置')
    return
  }

  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    let configs = []

    if (cached) {
      configs = JSON.parse(cached)
    }

    const rowId = props.editData.id
    const existingIndex = configs.findIndex((config: any) => config.rowId === rowId)

    const configData = {
      rowId: rowId,
      partitionName: props.editData.partitionName,
      datasetName: props.editData.datasetName,
      businessTable: props.editData.businessTable,
      sourceTable: form.value.sourceTable,
      targetTable: form.value.targetTable,
      ruleType: form.value.ruleType,
      ruleDescription: form.value.ruleDescription,
      enabled: form.value.enabled,
      createTime: existingIndex >= 0 ? configs[existingIndex].createTime : new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }

    if (existingIndex >= 0) {
      // 更新现有配置
      configs[existingIndex] = configData
      console.log('更新转换规则配置:', configData)
    } else {
      // 新增配置
      configs.push(configData)
      console.log('新增转换规则配置:', configData)
    }

    localStorage.setItem(STORAGE_KEY, JSON.stringify(configs))
  } catch (error) {
    console.error('保存转换规则配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    sourceTable: '',
    targetTable: '',
    ruleType: 'mapping',
    ruleDescription: '',
    enabled: true
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  // 验证必填项
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    ElMessage.error(`请完善以下信息：\n${errors.join('\n')}`)
    return
  }

  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    
    const transformData = {
      sourceTable: form.value.sourceTable,
      targetTable: form.value.targetTable,
      ruleType: form.value.ruleType,
      ruleDescription: form.value.ruleDescription,
      enabled: form.value.enabled
    }

    emit('confirm', transformData)
    ElMessage.success('数据转换规则设置成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 处理编辑数据的函数
const handleEditData = (editData: any) => {
  if (!editData) {
    resetForm()
    return
  }

  console.log('数据转换规则 - 处理编辑数据:', editData)

  // 首先尝试基于行ID查找已保存的配置
  if (editData.id) {
    const savedConfig = findConfigByRowId(editData.id)
    if (savedConfig) {
      console.log('找到已保存的转换规则配置:', savedConfig)
      // 使用已保存的配置数据
      form.value = {
        sourceTable: savedConfig.sourceTable || '',
        targetTable: savedConfig.targetTable || '',
        ruleType: savedConfig.ruleType || 'mapping',
        ruleDescription: savedConfig.ruleDescription || '',
        enabled: savedConfig.enabled !== undefined ? savedConfig.enabled : true
      }
      console.log('回显已保存的配置数据:', form.value)
      return
    }
  }

  // 如果没有找到已保存的配置，使用表格行数据生成默认配置
  console.log('未找到已保存配置，使用表格行数据生成默认配置')
  const businessTableName = editData.businessTable || ''
  const partitionName = editData.partitionName || ''
  const datasetName = editData.datasetName || ''
  const description = `为分区"${partitionName}"的数据集"${datasetName}"配置数据转换规则`

  console.log('生成的描述:', description)
  console.log('业务表名称:', businessTableName)

  // 根据业务表名称查找对应的源表
  const sourceTableValue = businessTableName ? getTableValueByLabel(businessTableName) : ''

  form.value = {
    sourceTable: sourceTableValue,
    targetTable: '',
    ruleType: 'mapping',
    ruleDescription: description,
    enabled: true
  }
  console.log('设置后的表单数据:', form.value)
}

// 监听弹窗打开状态
watch(() => props.modelValue, (newVal) => {
  console.log('数据转换规则弹窗状态变化:', newVal, '编辑数据:', props.editData)
  if (newVal) {
    // 每次打开弹窗时都重置表单，确保数据是干净的
    resetForm()

    // 使用nextTick确保DOM更新后再处理数据回显
    nextTick(() => {
      if (props.editData) {
        console.log('开始处理数据回显:', props.editData)
        handleEditData(props.editData)
      }
    })
  }
})

// 监听编辑数据变化
watch(() => props.editData, (newEditData) => {
  console.log('编辑数据变化:', newEditData)
  if (props.modelValue && newEditData) {
    handleEditData(newEditData)
  }
}, { deep: true })

// 根据表名称获取对应的值
const getTableValueByLabel = (label: string) => {
  // 确保 businessTables 是数组
  if (!Array.isArray(businessTables.value)) {
    console.warn('businessTables 不是数组:', businessTables.value)
    return ''
  }
  const table = businessTables.value.find(t => t.label.includes(label))
  return table ? table.value : ''
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px;
  min-height: 400px;

  .el-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .el-radio-group {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .el-radio {
        margin-right: 0;
      }
    }
  }
}
</style>
